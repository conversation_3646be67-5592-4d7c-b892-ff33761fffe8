@extends('layouts.main.app')
@section('head')
    @include('layouts.main.headersection', [
        'title' => 'All Templates',
        'buttons' => [
            [
                'name' => 'Create Template',
                'url' => route('user.template.create-now', ['device_id' => $device->uuid]),
            ],
        ],
    ])
@endsection
@section('content')
    <style>
        .grid-view {
            border: 1px dotted black;
            border-radius: 5px;
        }
    </style>
    <div class="row justify-content-center">
        <div class="col-12">
            @if ($message = Session::get('success'))
                <div class="alert alert-success alert-block" id="success-alert">
                    <button type="button" class="close" data-dismiss="alert">×</button>
                    <strong>{{ $message }}</strong>
                </div>
            @elseif ($message = Session::get('error'))
                <div class="alert alert-danger alert-block" id="error-alert">
                    <button type="button" class="close" data-dismiss="alert">×</button>
                    <strong>{{ $message }}</strong>
                </div>
            @endif
            <div class="row d-flex justify-content-between flex-wrap">
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers">
                                        {{ $total_templates }}
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-layers mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0">{{ __('Total Templates') }}</h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers">
                                        {{ $approved_tamplate }}
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-template mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm"></p>
                            <h5 class="card-title  text-muted mb-0">{{ __('Approved Templates') }}</h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 completed-transfers">
                                        {{ $pending_template }}
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi  fi-rs-exclamation mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0">{{ __('Pending Templates') }}</h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 completed-transfers">
                                        {{ $rejected_template }}
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi  fi-rs-cross mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0">{{ __('Rejected Templates') }}</h5>
                            <p></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col">
            <div class="card">
                <!-- Card header -->
                <div class="card-header border-0">
                    <h3 class="mb-0">{{ __('Template List') }}</h3>
                    <form action="" class="card-header-form">
                        {{-- for toggle list and grid --}}
                        <div id="view-toggle">
                            <span id="list-view-btn" class="view-icon" style="cursor: pointer;">
                                <svg class="svg-icon" viewBox="0 0 20 20" width="24" height="24" fill="currentColor">
                                    <path
                                        d="M10,1.75c-4.557,0-8.25,3.693-8.25,8.25c0,4.557,3.693,8.25,8.25,8.25c4.557,0,8.25-3.693,8.25-8.25C18.25,5.443,14.557,1.75,10,1.75 M10,17.382c-4.071,0-7.381-3.312-7.381-7.382c0-4.071,3.311-7.381,7.381-7.381c4.07,0,7.381,3.311,7.381,7.381C17.381,14.07,14.07,17.382,10,17.382 M7.612,10.869c-0.838,0-1.52,0.681-1.52,1.519s0.682,1.521,1.52,1.521c0.838,0,1.52-0.683,1.52-1.521S8.45,10.869,7.612,10.869 M7.612,13.039c-0.359,0-0.651-0.293-0.651-0.651c0-0.357,0.292-0.65,0.651-0.65c0.358,0,0.651,0.293,0.651,0.65C8.263,12.746,7.97,13.039,7.612,13.039 M7.629,6.11c-0.838,0-1.52,0.682-1.52,1.52c0,0.838,0.682,1.521,1.52,1.521c0.838,0,1.521-0.682,1.521-1.521C9.15,6.792,8.468,6.11,7.629,6.11M7.629,8.281c-0.358,0-0.651-0.292-0.651-0.651c0-0.358,0.292-0.651,0.651-0.651c0.359,0,0.651,0.292,0.651,0.651C8.281,7.988,7.988,8.281,7.629,8.281 M12.375,10.855c-0.838,0-1.521,0.682-1.521,1.52s0.683,1.52,1.521,1.52s1.52-0.682,1.52-1.52S13.213,10.855,12.375,10.855 M12.375,13.026c-0.358,0-0.652-0.294-0.652-0.651c0-0.358,0.294-0.652,0.652-0.652c0.357,0,0.65,0.294,0.65,0.652C13.025,12.732,12.732,13.026,12.375,13.026 M12.389,6.092c-0.839,0-1.52,0.682-1.52,1.52c0,0.838,0.681,1.52,1.52,1.52c0.838,0,1.52-0.681,1.52-1.52C13.908,6.774,13.227,6.092,12.389,6.092 M12.389,8.263c-0.36,0-0.652-0.293-0.652-0.651c0-0.359,0.292-0.651,0.652-0.651c0.357,0,0.65,0.292,0.65,0.651C13.039,7.97,12.746,8.263,12.389,8.263">
                                    </path>
                                </svg>
                            </span>
                            <span id="grid-view-btn" class="view-icon" style="cursor: pointer;">
                                <svg class="svg-icon" viewBox="0 0 20 20" width="24" height="24" fill="currentColor">
                                    <path
                                        d="M10,1.529c-4.679,0-8.471,3.792-8.471,8.471c0,4.68,3.792,8.471,8.471,8.471c4.68,0,8.471-3.791,8.471-8.471C18.471,5.321,14.68,1.529,10,1.529 M10,17.579c-4.18,0-7.579-3.399-7.579-7.579S5.82,2.421,10,2.421S17.579,5.82,17.579,10S14.18,17.579,10,17.579 M14.348,10c0,0.245-0.201,0.446-0.446,0.446h-5c-0.246,0-0.446-0.201-0.446-0.446s0.2-0.446,0.446-0.446h5C14.146,9.554,14.348,9.755,14.348,10 M14.348,12.675c0,0.245-0.201,0.446-0.446,0.446h-5c-0.246,0-0.446-0.201-0.446-0.446s0.2-0.445,0.446-0.445h5C14.146,12.229,14.348,12.43,14.348,12.675 M7.394,10c0,0.245-0.2,0.446-0.446,0.446H6.099c-0.245,0-0.446-0.201-0.446-0.446s0.201-0.446,0.446-0.446h0.849C7.194,9.554,7.394,9.755,7.394,10 M7.394,12.675c0,0.245-0.2,0.446-0.446,0.446H6.099c-0.245,0-0.446-0.201-0.446-0.446s0.201-0.445,0.446-0.445h0.849C7.194,12.229,7.394,12.43,7.394,12.675 M14.348,7.325c0,0.246-0.201,0.446-0.446,0.446h-5c-0.246,0-0.446-0.2-0.446-0.446c0-0.245,0.2-0.446,0.446-0.446h5C14.146,6.879,14.348,7.08,14.348,7.325 M7.394,7.325c0,0.246-0.2,0.446-0.446,0.446H6.099c-0.245,0-0.446-0.2-0.446-0.446c0-0.245,0.201-0.446,0.446-0.446h0.849C7.194,6.879,7.394,7.08,7.394,7.325">
                                    </path>

                                </svg>
                            </span>

                        </div>
                    </form>
                </div>
                <!-- Light table -->
                <div id="grid-view-container" class="container">
                    <div class="row">
                        @foreach ($templates as $template)
                            <div class="col-md-3 mb-4">
                                <div class="card grid-view">
                                    <div class="card-body">
                                        <h4 class="card-title">{{ $template['name'] }}&nbsp;
                                            <span
                                                class="badge badge-{{ $template['status'] == 'APPROVED' ? 'success' : ($template['status'] == 'PENDING' ? 'warning' : 'danger') }}">
                                                {{ $template['status'] }}
                                            </span>
                                        </h4>
                                        <div class="d-flex justify-content-between">
                                            <p class="card-text mb-2 text-bold" style="color:#825ee4;font-weight: bold;">
                                                {{ $template['category'] }}
                                            </p>
                                            <p style="color: black">
                                                {{ $template['language'] }}
                                            </p>
                                        </div>
                                        @foreach ($template['components'] as $component)
                                            @if ($component['type'] === 'HEADER')
                                                {{-- header component --}}
                                                @if (isset($component['example']) && isset($component['example']['header_handle'][0]))
                                                    @if ($component['format'] === 'IMAGE')
                                                        <div class="header-component">
                                                            <img src="{{ $component['example']['header_handle'][0] }}"
                                                                alt="Header Image" class="img-fluid">
                                                        </div>
                                                    @elseif ($component['format'] === 'VIDEO')
                                                        <div class="header-component">
                                                            <video class="img-fluid" alt="Header Video" controls>
                                                                <source
                                                                    src="{{ $component['example']['header_handle'][0] }}"
                                                                    type="video/mp4">
                                                            </video>
                                                            {{-- <img src="{{ $component['example']['header_handle'][0] }}"
                                                            alt="Header Image" class="img-fluid"> --}}
                                                        </div>
                                                    @elseif ($component['format'] === 'DOCUMENT')
                                                        <div class="header-component">
                                                            <img src="https://cdn4.iconfinder.com/data/icons/file-extensions-1/64/pdfs-512.png"
                                                                alt="Header Pdf" class="img-fluid">
                                                        </div>
                                                    @endif
                                                @endif
                                            @elseif ($component['type'] === 'BODY')
                                                {{-- body component --}}
                                                <div class="body-component mt-2">
                                                    @php
                                                        $text = $component['text'];

                                                        // Replace newline characters with <br> tags
                                                        $text = nl2br($text);

                                                        // Convert asterisks to <b> tags for bold text
                                                        $text = preg_replace('/\*(.*?)\*/', '<b>$1</b>', $text);

                                                        // Convert underscores to <i> tags for italic text
                                                        $text = preg_replace('/_(.*?)_/', '<i>$1</i>', $text);

                                                        // Convert backticks to <code> tags for monospace text
                                                        $text = preg_replace('/```(.*?)```/', '<code>$1</code>', $text);

                                                        // Convert tildes to <strike> tags for strike-through text
                                                        $text = preg_replace(
                                                            '/~~(.*?)~~/',
                                                            '<strike>$1</strike>',
                                                            $text,
                                                        );
                                                    @endphp

                                                    <p>{!! $text !!}</p>
                                                </div>
                                            @elseif ($component['type'] === 'BUTTONS')
                                                <!-- buttons component -->
                                                <div class="buttons-component text-center">
                                                    @foreach ($component['buttons'] as $button)
                                                        <div class="mt-2">
                                                            @if ($button['type'] === 'PHONE_NUMBER')
                                                                <a href="tel:{{ $button['phone_number'] }}"
                                                                    class="btn btn-sm btn-block btn-neutral">
                                                                    <i class="fi fi-rs-circle-phone-flip"></i>&nbsp;&nbsp;
                                                                    {{ $button['text'] }}
                                                                </a>
                                                            @elseif ($button['type'] === 'QUICK_REPLY')
                                                                <a href="#"
                                                                    class="btn btn-sm btn-block btn-neutral">
                                                                    <i class="fi fi-rs-reply-all"></i>&nbsp;&nbsp;
                                                                    {{ $button['text'] }}
                                                                </a>
                                                            @elseif ($button['type'] === 'URL')
                                                                <a href="{{ $button['url'] }}"
                                                                    class="btn btn-sm btn-block btn-neutral"
                                                                    target="_blank">
                                                                    <i
                                                                        class="fi fi-rs-arrow-up-right-from-square"></i>&nbsp;&nbsp;
                                                                    {{ $button['text'] }}
                                                                </a>
                                                            @endif
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>

                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div id="list-view-container" class="table-responsive">
                    <table class="table align-items-center table-flush">
                        <thead class="thead-light">
                            <tr>
                                <th class="col-1">{{ __('#') }}</th>
                                <th class="col-3">{{ __('Template Name') }}</th>
                                <th class="col-2">{{ __('Language') }}</th>
                                <th class="col-2">{{ __('Category') }}</th>
                                <th class="col-1 text-left">{{ __('Status') }}</th>
                            </tr>
                        </thead>
                        @if (count($templates) != 0)
                            <tbody class="list">
                                @php
                                    $i = 1;
                                @endphp
                                @foreach ($templates ?? [] as $template)
                                    <tr>
                                        <td>{{ $i }}</td>
                                        <td>
                                            {{ $template['name'] }}
                                        </td>
                                        <td>{{ $template['language'] }}</td>
                                        <td>{{ $template['category'] }}</td>
                                        <td>
                                            <span
                                                class="badge badge-{{ $template['status'] == 'APPROVED' ? 'success' : ($template['status'] == 'PENDING' ? 'warning' : 'danger') }}">
                                                {{ $template['status'] }}
                                            </span>
                                        </td>
                                    </tr>
                                    @php
                                        $i++;
                                    @endphp
                                @endforeach
                            </tbody>
                        @endif
                    </table>
                    @if (count($templates) == 0)
                        <div class="text-center mt-2">
                            <div class="alert  bg-gradient-primary text-white">
                                <span class="text-left">{{ __('!Opps no template found') }}</span>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="card-footer py-4">
                    {{-- <div class="d-flex justify-content-center">{{ $templates->links('vendor.pagination.bootstrap-4') }}</div> --}}
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="{{ asset('assets/js/pages/user/template-index.js') }}"></script>
    <script>
        setTimeout(function() {
            document.getElementById('success-alert')?.remove();
            document.getElementById('error-alert')?.remove();
        }, 10000);
    </script>
    <script>
        $(document).ready(function() {
            // hide and show
            $('#list-view-container').hide();
            $('#grid-view-container').show();

            // change grid
            $('#grid-view-btn').on('click', function() {
                $('#list-view-container').show();
                $('#grid-view-container').hide();
            });

            // change list
            $('#list-view-btn').on('click', function() {
                $('#grid-view-container').show();
                $('#list-view-container').hide();
            });
        });
    </script>
@endpush
