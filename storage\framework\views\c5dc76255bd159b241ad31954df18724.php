
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => 'All Templates',
        'buttons' => [
            [
                'name' => 'Create Template',
                'url' => route('user.template.create-now', ['device_id' => $device->uuid]),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <style>
        .grid-view {
            border: 1px dotted black;
            border-radius: 5px;
        }

        /* RCS Template Preview Styles */
        .rcs-message-bubble {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 3px solid #007bff;
        }

        .rcs-rich-card {
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .rcs-card-media img {
            border-radius: 8px;
        }

        .rcs-carousel {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
        }

        .rcs-carousel-card {
            background-color: #ffffff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .rcs-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .rcs-suggestions .btn {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        .rcs-media-container {
            text-align: center;
        }

        .rcs-media-container img {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .template-preview-card {
            min-height: 300px;
            max-height: 400px;
            overflow-y: auto;
        }

        .template-type-badge {
            font-size: 0.7rem;
            padding: 2px 6px;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if($message = Session::get('success')): ?>
                <div class="alert alert-success alert-block" id="success-alert">
                    <button type="button" class="close" data-dismiss="alert">×</button>
                    <strong><?php echo e($message); ?></strong>
                </div>
            <?php elseif($message = Session::get('error')): ?>
                <div class="alert alert-danger alert-block" id="error-alert">
                    <button type="button" class="close" data-dismiss="alert">×</button>
                    <strong><?php echo e($message); ?></strong>
                </div>
            <?php endif; ?>
            <div class="row d-flex justify-content-between flex-wrap">
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers">
                                        <?php echo e($total_templates); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-layers mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Total Templates')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers">
                                        <?php echo e($templates->where('status', 1)->count()); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-success text-white rounded-circle shadow">
                                        <i class="fi fi-rs-check mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm"></p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Active Templates')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 completed-transfers">
                                        <?php echo e($templates->where('type', 'rich-card')->count()); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                        <i class="fi fi-rs-layers mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Rich Cards')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 completed-transfers">
                                        <?php echo e($templates->where('type', 'rich-card-carousel')->count()); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-warning text-white rounded-circle shadow">
                                        <i class="fi fi-rs-apps mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Carousels')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col">
            <div class="card">
                <!-- Card header -->
                <div class="card-header border-0">
                    <h3 class="mb-0"><?php echo e(__('Template List')); ?></h3>
                    <form action="" class="card-header-form">
                        
                        <div id="view-toggle">
                            <span id="list-view-btn" class="view-icon" style="cursor: pointer;">
                                <svg class="svg-icon" viewBox="0 0 20 20" width="24" height="24" fill="currentColor">
                                    <path
                                        d="M10,1.75c-4.557,0-8.25,3.693-8.25,8.25c0,4.557,3.693,8.25,8.25,8.25c4.557,0,8.25-3.693,8.25-8.25C18.25,5.443,14.557,1.75,10,1.75 M10,17.382c-4.071,0-7.381-3.312-7.381-7.382c0-4.071,3.311-7.381,7.381-7.381c4.07,0,7.381,3.311,7.381,7.381C17.381,14.07,14.07,17.382,10,17.382 M7.612,10.869c-0.838,0-1.52,0.681-1.52,1.519s0.682,1.521,1.52,1.521c0.838,0,1.52-0.683,1.52-1.521S8.45,10.869,7.612,10.869 M7.612,13.039c-0.359,0-0.651-0.293-0.651-0.651c0-0.357,0.292-0.65,0.651-0.65c0.358,0,0.651,0.293,0.651,0.65C8.263,12.746,7.97,13.039,7.612,13.039 M7.629,6.11c-0.838,0-1.52,0.682-1.52,1.52c0,0.838,0.682,1.521,1.52,1.521c0.838,0,1.521-0.682,1.521-1.521C9.15,6.792,8.468,6.11,7.629,6.11M7.629,8.281c-0.358,0-0.651-0.292-0.651-0.651c0-0.358,0.292-0.651,0.651-0.651c0.359,0,0.651,0.292,0.651,0.651C8.281,7.988,7.988,8.281,7.629,8.281 M12.375,10.855c-0.838,0-1.521,0.682-1.521,1.52s0.683,1.52,1.521,1.52s1.52-0.682,1.52-1.52S13.213,10.855,12.375,10.855 M12.375,13.026c-0.358,0-0.652-0.294-0.652-0.651c0-0.358,0.294-0.652,0.652-0.652c0.357,0,0.65,0.294,0.65,0.652C13.025,12.732,12.732,13.026,12.375,13.026 M12.389,6.092c-0.839,0-1.52,0.682-1.52,1.52c0,0.838,0.681,1.52,1.52,1.52c0.838,0,1.52-0.681,1.52-1.52C13.908,6.774,13.227,6.092,12.389,6.092 M12.389,8.263c-0.36,0-0.652-0.293-0.652-0.651c0-0.359,0.292-0.651,0.652-0.651c0.357,0,0.65,0.292,0.65,0.651C13.039,7.97,12.746,8.263,12.389,8.263">
                                    </path>
                                </svg>
                            </span>
                            <span id="grid-view-btn" class="view-icon" style="cursor: pointer;">
                                <svg class="svg-icon" viewBox="0 0 20 20" width="24" height="24" fill="currentColor">
                                    <path
                                        d="M10,1.529c-4.679,0-8.471,3.792-8.471,8.471c0,4.68,3.792,8.471,8.471,8.471c4.68,0,8.471-3.791,8.471-8.471C18.471,5.321,14.68,1.529,10,1.529 M10,17.579c-4.18,0-7.579-3.399-7.579-7.579S5.82,2.421,10,2.421S17.579,5.82,17.579,10S14.18,17.579,10,17.579 M14.348,10c0,0.245-0.201,0.446-0.446,0.446h-5c-0.246,0-0.446-0.201-0.446-0.446s0.2-0.446,0.446-0.446h5C14.146,9.554,14.348,9.755,14.348,10 M14.348,12.675c0,0.245-0.201,0.446-0.446,0.446h-5c-0.246,0-0.446-0.201-0.446-0.446s0.2-0.445,0.446-0.445h5C14.146,12.229,14.348,12.43,14.348,12.675 M7.394,10c0,0.245-0.2,0.446-0.446,0.446H6.099c-0.245,0-0.446-0.201-0.446-0.446s0.201-0.446,0.446-0.446h0.849C7.194,9.554,7.394,9.755,7.394,10 M7.394,12.675c0,0.245-0.2,0.446-0.446,0.446H6.099c-0.245,0-0.446-0.201-0.446-0.446s0.201-0.445,0.446-0.445h0.849C7.194,12.229,7.394,12.43,7.394,12.675 M14.348,7.325c0,0.246-0.201,0.446-0.446,0.446h-5c-0.246,0-0.446-0.2-0.446-0.446c0-0.245,0.2-0.446,0.446-0.446h5C14.146,6.879,14.348,7.08,14.348,7.325 M7.394,7.325c0,0.246-0.2,0.446-0.446,0.446H6.099c-0.245,0-0.446-0.2-0.446-0.446c0-0.245,0.201-0.446,0.446-0.446h0.849C7.194,6.879,7.394,7.08,7.394,7.325">
                                    </path>

                                </svg>
                            </span>

                        </div>
                    </form>
                </div>
                <!-- RCS Templates Grid View -->
                <div id="grid-view-container" class="container">
                    <div class="row">
                        <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-4 mb-4">
                                <div class="card grid-view template-preview-card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="card-title mb-0"><?php echo e($template->title); ?></h6>
                                        <div>
                                            <span
                                                class="badge badge-<?php echo e($template->status == 1 ? 'success' : 'danger'); ?> template-type-badge">
                                                <?php echo e($template->status == 1 ? 'Active' : 'Inactive'); ?>

                                            </span>
                                            <span class="badge badge-info template-type-badge ml-1">
                                                <?php echo e(ucfirst(str_replace('-', ' ', $template->type))); ?>

                                            </span>
                                        </div>
                                    </div>
                                    <div class="card-body p-2"
                                        style="background-image: url('<?php echo e(asset('assets/img/whatsappback.jpg')); ?>'); background-size: cover;">
                                        <div class="template-preview-content" data-template="<?php echo e(json_encode($template)); ?>">
                                            <!-- Template preview will be rendered here by JavaScript -->
                                        </div>
                                    </div>
                                    <div class="card-footer text-center">
                                        <small class="text-muted">Created:
                                            <?php echo e($template->created_at->format('M d, Y')); ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <div id="list-view-container" class="table-responsive">
                    <table class="table align-items-center table-flush">
                        <thead class="thead-light">
                            <tr>
                                <th class="col-1"><?php echo e(__('#')); ?></th>
                                <th class="col-3"><?php echo e(__('Template Name')); ?></th>
                                <th class="col-2"><?php echo e(__('Type')); ?></th>
                                <th class="col-2"><?php echo e(__('Created Date')); ?></th>
                                <th class="col-1 text-left"><?php echo e(__('Status')); ?></th>
                                <th class="col-1 text-center"><?php echo e(__('Actions')); ?></th>
                            </tr>
                        </thead>
                        <?php if(count($templates) != 0): ?>
                            <tbody class="list">
                                <?php
                                    $i = 1;
                                ?>
                                <?php $__currentLoopData = $templates ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($i); ?></td>
                                        <td>
                                            <strong><?php echo e($template->title); ?></strong>
                                            <br>
                                            <small class="text-muted">ID: <?php echo e($template->id); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                <?php echo e(ucfirst(str_replace('-', ' ', $template->type))); ?>

                                            </span>
                                        </td>
                                        <td><?php echo e($template->created_at->format('M d, Y H:i')); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo e($template->status == 1 ? 'success' : 'danger'); ?>">
                                                <?php echo e($template->status == 1 ? 'Active' : 'Inactive'); ?>

                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <a href="<?php echo e(route('user.template.edit', $template->id)); ?>"
                                                class="btn btn-sm btn-outline-primary" title="Edit Template">
                                                <i class="fi fi-rs-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php
                                        $i++;
                                    ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        <?php endif; ?>
                    </table>
                    <?php if(count($templates) == 0): ?>
                        <div class="text-center mt-2">
                            <div class="alert bg-gradient-primary text-white">
                                <span class="text-left"><?php echo e(__('!Opps no templates found')); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="card-footer py-4">
                    
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/js/pages/user/template-index.js')); ?>"></script>
    <script>
        setTimeout(function() {
            document.getElementById('success-alert')?.remove();
            document.getElementById('error-alert')?.remove();
        }, 10000);
    </script>
    <script>
        $(document).ready(function() {
            // hide and show
            $('#list-view-container').hide();
            $('#grid-view-container').show();

            // change grid
            $('#grid-view-btn').on('click', function() {
                $('#list-view-container').show();
                $('#grid-view-container').hide();
            });

            // change list
            $('#list-view-btn').on('click', function() {
                $('#grid-view-container').show();
                $('#list-view-container').hide();
            });

            // Render template previews
            renderTemplatePreview();
        });

        function renderTemplatePreview() {
            $('.template-preview-content').each(function() {
                var $container = $(this);
                var templateData = $container.data('template');

                if (templateData && templateData.body) {
                    displayTemplatePreview($container, templateData);
                } else {
                    $container.html('<p class="text-muted small">No preview available</p>');
                }
            });
        }

        function displayTemplatePreview($container, template) {
            $container.empty();

            // Handle different template types
            if (template.type === 'plain-text') {
                displayPlainTextTemplate($container, template);
            } else if (template.type === 'text-with-media') {
                displayMediaTemplate($container, template);
            } else if (template.type === 'rich-card') {
                displayRichCardTemplate($container, template);
            } else if (template.type === 'rich-card-carousel') {
                displayCarouselTemplate($container, template);
            } else {
                // Legacy template types (numeric)
                displayLegacyTemplate($container, template);
            }
        }

        function displayPlainTextTemplate($container, template) {
            if (template.body && template.body.content && template.body.content.plainText) {
                $container.append(
                    '<div class="rcs-message-bubble">' +
                    '<p class="card-text small">' + formatWhatsAppText(template.body.content.plainText) + '</p>' +
                    '</div>'
                );
            }

            // Display suggestions if any
            if (template.body && template.body.content && template.body.content.suggestions) {
                displaySuggestions($container, template.body.content.suggestions);
            }
        }

        function displayMediaTemplate($container, template) {
            if (template.body && template.body.content) {
                var content = template.body.content;

                // Display media if present
                if (content.rcs_media_url || content.local_media_url) {
                    var mediaUrl = content.rcs_media_url || content.local_media_url;
                    $container.append(
                        '<div class="rcs-media-container mb-2">' +
                        '<img src="' + mediaUrl +
                        '" class="img-fluid rounded" style="max-width: 200px; max-height: 150px;">' +
                        '</div>'
                    );
                }

                // Display text
                if (content.plainText) {
                    $container.append(
                        '<div class="rcs-message-bubble">' +
                        '<p class="card-text small">' + formatWhatsAppText(content.plainText) + '</p>' +
                        '</div>'
                    );
                }

                // Display suggestions
                if (content.suggestions) {
                    displaySuggestions($container, content.suggestions);
                }
            }
        }

        function displayRichCardTemplate($container, template) {
            if (template.body && template.body.content && template.body.content.richCardDetails) {
                var richCard = template.body.content.richCardDetails.standalone;
                if (richCard && richCard.content) {
                    var cardContent = richCard.content;

                    // Create rich card container with orientation support
                    var orientation = richCard.cardOrientation || 'VERTICAL';
                    var cardClass = orientation === 'HORIZONTAL' ? 'd-flex' : '';
                    var cardHtml = '<div class="rcs-rich-card border rounded p-2 mb-2 ' + cardClass +
                        '" style="max-width: 220px;">';

                    // Display card media if present
                    if (cardContent.cardMedia && cardContent.cardMedia.contentInfo && cardContent.cardMedia.contentInfo
                        .fileUrl) {
                        var mediaStyle = orientation === 'HORIZONTAL' ?
                            'width: 80px; height: 60px; object-fit: cover; margin-right: 8px;' :
                            'width: 100%; height: 100px; object-fit: cover;';

                        cardHtml += '<div class="rcs-card-media mb-1">';
                        cardHtml += '<img src="' + cardContent.cardMedia.contentInfo.fileUrl +
                            '" class="img-fluid rounded" style="' + mediaStyle + '">';
                        cardHtml += '</div>';
                    }

                    // Content container for horizontal layout
                    if (orientation === 'HORIZONTAL') {
                        cardHtml += '<div class="flex-grow-1">';
                    }

                    // Display card title
                    if (cardContent.cardTitle) {
                        cardHtml += '<h6 class="rcs-card-title font-weight-bold mb-1 small">' + cardContent.cardTitle +
                            '</h6>';
                    }

                    // Display card description
                    if (cardContent.cardDescription) {
                        cardHtml += '<p class="rcs-card-description text-muted mb-1" style="font-size: 0.7rem;">' +
                            formatWhatsAppText(cardContent.cardDescription) + '</p>';
                    }

                    // Close content container for horizontal layout
                    if (orientation === 'HORIZONTAL') {
                        cardHtml += '</div>';
                    }

                    cardHtml += '</div>';
                    $container.append(cardHtml);

                    // Display suggestions if any (they are optional)
                    if (cardContent.suggestions && cardContent.suggestions.length > 0) {
                        displaySuggestions($container, cardContent.suggestions);
                    }
                }
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/template/view.blade.php ENDPATH**/ ?>