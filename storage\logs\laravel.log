[2025-05-30 10:24:31] local.INFO: data is {"task_id":35,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:24:28","send_to_number":"919510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"573a1c41-5bb0-45a7-b709-564302e45f9f","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:24:31] local.DEBUG: {"message":"OK","referenceID":"86ee36f9-a737-4256-a128-8a3b2239e480"}  
[2025-05-30 10:24:31] local.DEBUG: Message Data: {"messageID":"573a1c41-5bb0-45a7-b709-564302e45f9f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919510991141"],"data":[{"content":{"plainText":"Hellow Template Desctiption Test"}}],"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:27:22] local.INFO: data is {"task_id":36,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:27:22","send_to_number":"919510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"229863b5-a90b-4b04-9c99-521c3ace89a2","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:27:23] local.DEBUG: {"message":"OK","referenceID":"cf170cfa-2028-458f-b8bc-f325de0d7e74"}  
[2025-05-30 10:27:23] local.DEBUG: Message Data: {"messageID":"229863b5-a90b-4b04-9c99-521c3ace89a2","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:29:37] local.INFO: data is {"task_id":37,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:29:32","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"844e6b6c-a67d-498a-a145-f1a102d317ae","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:29:38] local.DEBUG: {"message":"OK","referenceID":"b8926956-25d3-4065-b88b-18d5e671ac62"}  
[2025-05-30 10:29:38] local.DEBUG: Message Data: {"messageID":"844e6b6c-a67d-498a-a145-f1a102d317ae","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:29:38] local.INFO: data is {"task_id":38,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:29:32","send_to_number":"8320677031","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"7821d0b1-e2e1-4d62-8734-a5a1c60be04e","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:29:38] local.DEBUG: {"message":"OK","referenceID":"7e8f29c1-2bcd-40aa-a7cd-239c1357c50f"}  
[2025-05-30 10:29:38] local.DEBUG: Message Data: {"messageID":"7821d0b1-e2e1-4d62-8734-a5a1c60be04e","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["8320677031"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:30:30] local.INFO: data is {"task_id":39,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:30:26","send_to_number":"9510991141","templateId":"10","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"784fc582-5d3a-47f5-85c9-fe8cb5047d3a","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:30:30] local.DEBUG: {"message":"OK","referenceID":"9121b118-0437-477d-997a-83ec7a5204dc"}  
[2025-05-30 10:30:30] local.DEBUG: Message Data: {"messageID":"784fc582-5d3a-47f5-85c9-fe8cb5047d3a","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"Rich Card","cardDescription":"Rich Card Description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/17485802362.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/17485802362.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748580237.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Reply","postBack":{"data":"reply_suggestion_1"}}},{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2"},"dialerAction":{"phoneNumber":"+91 8320677031"}}},{"action":{"plainText":"Click Here","postBack":{"data":"url_suggestion_3"},"openUrl":{"url":"www.google.com"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:39:06] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"68393d726a70e24038e75172\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748581745105.png\",\"status\":\"success\"}","file_name":"NXC_new_logo_white.png","file_size":41913,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 10:39:43] local.INFO: data is {"task_id":40,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:39:42","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"51a0b021-9c2d-4ee6-92e6-b208d71b5709","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:39:44] local.DEBUG: {"message":"OK","referenceID":"44815832-6c8b-4fc2-b4d4-fb201b3e2eef"}  
[2025-05-30 10:39:44] local.DEBUG: Message Data: {"messageID":"51a0b021-9c2d-4ee6-92e6-b208d71b5709","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"VERTICAL","content":{"cardTitle":"Card Title","cardDescription":"Card Description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748581745105.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748581745105.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748581746.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"testingt t","postBack":{"data":"reply_suggestion_1"}}},{"action":{"plainText":"Visit Now","postBack":{"data":"url_suggestion_2"},"openUrl":{"url":"www.google.com"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:40:59] local.INFO: data is {"task_id":41,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:40:58","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"239fc1b8-fccf-4a27-9670-89fafc58a376","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:41:00] local.DEBUG: {"message":"OK","referenceID":"260fc7e3-6a87-4efa-a233-0c662e6579ea"}  
[2025-05-30 10:41:00] local.DEBUG: Message Data: {"messageID":"239fc1b8-fccf-4a27-9670-89fafc58a376","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"VERTICAL","content":{"cardTitle":"card title","cardDescription":"description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/66e025b800adf197e64fffbc\/1727684177481.png"}},"suggestions":[{"action":{"plainText":"Visit Now","postBack":{"data":"visit_now_election24{{$50}}"},"openUrl":{"url":"https:\/\/elections24.eci.gov.in\/"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:49:22] local.INFO: data is {"task_id":42,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:49:19","send_to_number":"9510991141","templateId":"8","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"53454a25-7c90-4150-8912-d5ef7877ebbd","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:50:43] local.INFO: data is {"task_id":43,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:50:41","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a2bdc1e0-f068-4aff-ba97-0469f3f9b75f","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:51:27] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:51:44] local.INFO: data is {"task_id":44,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:51:42","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"19dfb9d9-ff17-4a25-8f07-8fc40539ceb6","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:52:00] local.INFO: data is {"task_id":45,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:51:59","send_to_number":"95410991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"fb84a57f-97da-4ce0-a3f7-c9a4c37a53ca","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:54:41] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:54:41] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:54:41] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:55:05] local.INFO: data is {"task_id":46,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:55:04","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"5a24ac39-b121-4591-b54d-dde54b10ded3","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:42:47] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 11:43:05] local.INFO: data is {"task_id":47,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:43:05","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9caf3cac-8f0b-43cb-a514-321b2fef7cd6","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:43:05] local.DEBUG: {"message":"OK","referenceID":"8de37bed-2431-42eb-b130-bc92717de37a"}  
[2025-05-30 11:43:05] local.DEBUG: Message Data: {"messageID":"9caf3cac-8f0b-43cb-a514-321b2fef7cd6","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:43:05] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:43:44] local.INFO: data is {"task_id":48,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:43:41","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"b67decf5-755f-4179-89af-5c058966488e","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:43:44] local.DEBUG: {"message":"OK","referenceID":"5c83c691-0ba9-440d-8f4e-a4700ca3be67"}  
[2025-05-30 11:43:44] local.DEBUG: Message Data: {"messageID":"b67decf5-755f-4179-89af-5c058966488e","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:43:44] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:47:14] local.ERROR: App\Jobs\CheckCapability has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\CheckCapability has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 11:47:14] local.ERROR: App\Jobs\CheckCapability has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\CheckCapability has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 11:47:26] local.INFO: data is {"task_id":49,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:47:24","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"f9bb9cbc-13c7-4cae-8762-3508d8dbd86c","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:47:26] local.DEBUG: {"message":"OK","referenceID":"c4d46969-4876-4551-803c-c1c6b7d3de5e"}  
[2025-05-30 11:47:26] local.DEBUG: Message Data: {"messageID":"f9bb9cbc-13c7-4cae-8762-3508d8dbd86c","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:47:26] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:47:27] local.DEBUG: [true]  
[2025-05-30 11:49:29] local.INFO: data is {"task_id":50,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:49:28","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"935f5f4b-7b06-4e9e-aa8d-e8539939a076","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:49:30] local.DEBUG: {"message":"OK","referenceID":"0a8acbb1-b988-4de6-af16-32f36691fb8e"}  
[2025-05-30 11:49:30] local.DEBUG: Message Data: {"messageID":"935f5f4b-7b06-4e9e-aa8d-e8539939a076","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:49:30] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:49:30] local.INFO: Number 9510991141 is capable.  
[2025-05-30 11:50:35] local.INFO: data is {"task_id":51,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:50:33","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"6ce9e8cd-4ae9-4e70-a747-38c888dcf11f","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:50:36] local.DEBUG: {"message":"OK","referenceID":"58f8b56f-bfc7-45c9-9942-0117546c4ec6"}  
[2025-05-30 11:50:36] local.DEBUG: Message Data: {"messageID":"6ce9e8cd-4ae9-4e70-a747-38c888dcf11f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:50:36] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:50:37] local.INFO: Number 9510991141 is capable.  
[2025-05-30 11:51:07] local.INFO: data is {"task_id":52,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:51:05","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"352a8666-d828-4c14-b80a-d5e5a48b6509","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:51:07] local.DEBUG: {"message":"OK","referenceID":"1fa9902b-2699-48ce-a08c-a7ca852ed170"}  
[2025-05-30 11:51:07] local.DEBUG: Message Data: {"messageID":"352a8666-d828-4c14-b80a-d5e5a48b6509","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:51:07] local.INFO: data is {"task_id":53,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:51:05","send_to_number":"8320677031","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"5178c03b-4678-439e-9e34-e82fc3a88c8f","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:51:08] local.DEBUG: {"message":"OK","referenceID":"84e3cda3-1556-4cb0-bc52-f0c2ab978565"}  
[2025-05-30 11:51:08] local.DEBUG: Message Data: {"messageID":"5178c03b-4678-439e-9e34-e82fc3a88c8f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["8320677031"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:51:08] local.INFO: data is {"task_id":54,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:51:05","send_to_number":"9484403354","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"0241f0f7-f8ac-4043-abee-443ceff2219f","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:51:08] local.DEBUG: {"message":"OK","referenceID":"92c31db2-9828-43ca-bf85-987e069455eb"}  
[2025-05-30 11:51:08] local.DEBUG: Message Data: {"messageID":"0241f0f7-f8ac-4043-abee-443ceff2219f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9484403354"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:51:08] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:51:09] local.INFO: Number 9510991141 is capable.  
[2025-05-30 11:51:09] local.INFO: Inside job handle for number: 8320677031  
[2025-05-30 11:51:10] local.INFO: Number 8320677031 is not capable.  
[2025-05-30 11:51:10] local.INFO: Inside job handle for number: 9484403354  
[2025-05-30 11:51:10] local.INFO: Number 9484403354 is capable.  
[2025-05-30 14:30:04] local.INFO: data is {"task_id":55,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:30:03","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"959a1cc3-8f05-43e4-a4f4-df7b91cbbec8","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:30:05] local.DEBUG: {"message":"OK","referenceID":"82d7ba07-8288-4bce-89e4-73c2d58f318b"}  
[2025-05-30 14:30:05] local.DEBUG: Message Data: {"messageID":"959a1cc3-8f05-43e4-a4f4-df7b91cbbec8","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:30:05] local.ERROR: Attempt to read property "message" on array {"stacktrace":"#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\Users\\\\<USER>\\\\...', 192)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Traits\\Whatsapp.php(192): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\Users\\\\<USER>\\\\...', 192)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(45): App\\Jobs\\SendTaskJob->sendwhatsapp(Array, NULL)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): App\\Jobs\\SendTaskJob->App\\Jobs\\{closure}(Object(Illuminate\\Database\\MySqlConnection))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(509): Illuminate\\Database\\Connection->transaction(Object(Closure))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\SendTaskJob->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\SendTaskJob))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\SendTaskJob), false)
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\SendTaskJob))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\SendTaskJob))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}","request":""} 
[2025-05-30 14:30:05] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 14:30:05] local.INFO: Number 9510991141 is capable.  
[2025-05-30 14:31:25] local.INFO: data is {"task_id":56,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:31:22","send_to_number":"9510991141","templateId":"8","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a50d4fe8-e042-45d7-9aa6-8ae15a7da341","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:31:26] local.DEBUG: {"message":"OK","referenceID":"04ae1fe0-e8c1-4ceb-b2b3-78241546fe6f"}  
[2025-05-30 14:31:26] local.DEBUG: Message Data: {"messageID":"a50d4fe8-e042-45d7-9aa6-8ae15a7da341","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"asdf","cardDescription":"sadfasdfsadf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748434217981.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748434217981.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748434217.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"asdfasd","postBack":{"data":"reply_suggestion_1"}}},{"action":{"plainText":"sadfsad","postBack":{"data":"dial_suggestion_2"},"dialerAction":{"phoneNumber":"146546541"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:31:26] local.INFO: Message send okay with04ae1fe0-e8c1-4ceb-b2b3-78241546fe6f  
[2025-05-30 14:31:26] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 14:31:26] local.INFO: Number 9510991141 is capable.  
[2025-05-30 14:43:04] local.INFO: data is {"task_id":57,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:43:02","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a1540d9b-0fbb-4b38-8877-b294ce7a33c3","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:43:05] local.DEBUG: {"message":"OK","referenceID":"865d4622-4671-4483-9d72-39eeab268c0e"}  
[2025-05-30 14:43:05] local.DEBUG: Message Data: {"messageID":"a1540d9b-0fbb-4b38-8877-b294ce7a33c3","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:43:05] local.INFO: Message send okay with865d4622-4671-4483-9d72-39eeab268c0e  
[2025-05-30 14:43:05] local.ERROR: Array to string conversion {"stacktrace":"#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'C:\\\\Users\\\\<USER>\\\\...', 194)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Traits\\Whatsapp.php(194): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Array to string...', 'C:\\\\Users\\\\<USER>\\\\...', 194)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(45): App\\Jobs\\SendTaskJob->sendwhatsapp(Array, NULL)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): App\\Jobs\\SendTaskJob->App\\Jobs\\{closure}(Object(Illuminate\\Database\\MySqlConnection))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(509): Illuminate\\Database\\Connection->transaction(Object(Closure))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\SendTaskJob->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\SendTaskJob))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\SendTaskJob), false)
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\SendTaskJob))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\SendTaskJob))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}","request":""} 
[2025-05-30 14:44:51] local.INFO: data is {"task_id":58,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:44:48","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9148c23e-8c3e-4aab-8ab7-ea49d11fe7e0","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:44:52] local.DEBUG: {"message":"OK","referenceID":"4908668d-bf1a-49be-a8fc-ae279b68e4e5"}  
[2025-05-30 14:44:52] local.DEBUG: Message Data: {"messageID":"9148c23e-8c3e-4aab-8ab7-ea49d11fe7e0","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:44:52] local.INFO: Message send okay with4908668d-bf1a-49be-a8fc-ae279b68e4e5  
[2025-05-30 14:47:05] local.INFO: data is {"task_id":59,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:47:04","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"e6f7ab58-e6d2-424a-ae9f-9ab708d4214d","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:47:06] local.DEBUG: {"message":"OK","referenceID":"19c8201a-a9c3-439a-b8d7-5486f7a3b499"}  
[2025-05-30 14:47:06] local.DEBUG: Message Data: {"messageID":"e6f7ab58-e6d2-424a-ae9f-9ab708d4214d","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:47:06] local.INFO: Message send okay with19c8201a-a9c3-439a-b8d7-5486f7a3b499  
[2025-05-30 16:07:23] local.INFO: data is {"task_id":60,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:07:22","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"11556fe6-0f4e-461d-b70e-8f69e261add5","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:07:24] local.DEBUG: {"message":"OK","referenceID":"65ef183e-6beb-4577-8719-718675281e7a"}  
[2025-05-30 16:07:24] local.DEBUG: Message Data: {"messageID":"11556fe6-0f4e-461d-b70e-8f69e261add5","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"www.gogle.com"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 16:07:24] local.INFO: Message send okay with65ef183e-6beb-4577-8719-718675281e7a  
[2025-05-30 16:11:31] local.INFO: data is {"task_id":61,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:11:30","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"00172d05-a535-4c26-8751-073f107e1ed2","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:11:32] local.DEBUG: {"message":"OK","referenceID":"a24a4e30-1444-4b20-9fe5-17a2bba65a26"}  
[2025-05-30 16:11:32] local.DEBUG: Message Data: {"messageID":"00172d05-a535-4c26-8751-073f107e1ed2","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"www.gogle.com"}}}]}}}  
[2025-05-30 16:11:32] local.INFO: Message send okay witha24a4e30-1444-4b20-9fe5-17a2bba65a26  
[2025-05-30 16:14:36] local.INFO: data is {"task_id":62,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:14:34","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"d4a8a839-e8f5-4e46-907e-1bef30c5078c","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:14:36] local.DEBUG: {"message":"OK","referenceID":"3ef782a6-96fd-4a5b-8604-b8a497b597a4"}  
[2025-05-30 16:14:36] local.DEBUG: Message Data: {"messageID":"d4a8a839-e8f5-4e46-907e-1bef30c5078c","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hi, Welcome to NXC Controls Pvt Ltd.\ud83e\udd73\ud83c\udf89","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"call_back_data_for_button_1_goes_here{{$50}}"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:14:36] local.INFO: Message send okay with3ef782a6-96fd-4a5b-8604-b8a497b597a4  
[2025-05-30 16:17:49] local.INFO: data is {"task_id":63,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:17:48","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"185b9fa4-7ae1-411c-b48c-7296fb08ad32","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:17:50] local.DEBUG: {"message":"OK","referenceID":"fc78257c-69b9-4294-b227-dc77fd24f443"}  
[2025-05-30 16:17:50] local.DEBUG: Message Data: {"messageID":"185b9fa4-7ae1-411c-b48c-7296fb08ad32","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hi, Welcome to NXC Controls Pvt Ltd.\ud83e\udd73\ud83c\udf89","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"call_back_data_for_button_1_goes_here{{$50}}"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:17:50] local.INFO: Message send okay withfc78257c-69b9-4294-b227-dc77fd24f443  
[2025-05-30 16:18:41] local.INFO: data is {"task_id":64,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:18:38","send_to_number":"9510991141","templateId":"14","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"f4443b79-3611-44ef-9361-8ecc807bd1e7","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:18:42] local.DEBUG: {"message":"OK","referenceID":"aab0ae03-bb15-4b11-8283-9476d7facd4d"}  
[2025-05-30 16:18:42] local.DEBUG: Message Data: {"messageID":"f4443b79-3611-44ef-9361-8ecc807bd1e7","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"www.gogle.com"}}}]}}}  
[2025-05-30 16:18:42] local.INFO: Message send okay withaab0ae03-bb15-4b11-8283-9476d7facd4d  
[2025-05-30 16:20:28] local.INFO: data is {"task_id":65,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:20:25","send_to_number":"9510991141","templateId":"14","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"1f90204f-c3e6-45a5-bf94-99b227794945","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:20:28] local.DEBUG: {"message":"OK","referenceID":"9e928abd-21b1-4b8d-b777-63686ea60a83"}  
[2025-05-30 16:20:28] local.DEBUG: Message Data: {"messageID":"1f90204f-c3e6-45a5-bf94-99b227794945","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:20:28] local.INFO: Message send okay with9e928abd-21b1-4b8d-b777-63686ea60a83  
[2025-05-30 16:22:59] local.INFO: data is {"task_id":66,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:22:56","send_to_number":"9510991141","templateId":"14","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"fce5618c-bc49-49c0-aa0b-4a93fa0f99b4","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:22:59] local.DEBUG: {"message":"OK","referenceID":"b4c81389-96b3-4e86-8d0b-fa9c97e77a0c"}  
[2025-05-30 16:22:59] local.DEBUG: Message Data: {"messageID":"fce5618c-bc49-49c0-aa0b-4a93fa0f99b4","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"call_back_data_for_button_1_goes_here"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:22:59] local.INFO: Message send okay withb4c81389-96b3-4e86-8d0b-fa9c97e77a0c  
[2025-05-30 17:24:03] local.INFO: data is {"task_id":67,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:38:39","send_to_number":"9510991141","templateId":"17","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"0f941003-16b6-4711-84ac-4de51dc1c6e9","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:24:04] local.DEBUG: {"message":"OK","referenceID":"fd94662d-a0b8-49c3-aeea-21dc38934c59"}  
[2025-05-30 17:24:04] local.DEBUG: Message Data: {"messageID":"0f941003-16b6-4711-84ac-4de51dc1c6e9","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testttttttttttttttttttttttt","suggestions":[{"reply":{"plainText":"Call Us","postBack":{"data":"reply_suggestion_1"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:24:04] local.INFO: Message send okay withfd94662d-a0b8-49c3-aeea-21dc38934c59  
[2025-05-30 17:24:04] local.INFO: data is {"task_id":68,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:23:14","send_to_number":"9510991141","templateId":"18","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"51b63c13-ec97-47bc-a537-4668c8b3c865","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:24:04] local.DEBUG: {"message":"OK","referenceID":"408d2d88-cd87-47e5-ae1b-f3cb760ac36f"}  
[2025-05-30 17:24:04] local.DEBUG: Message Data: {"messageID":"51b63c13-ec97-47bc-a537-4668c8b3c865","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"asdfsadfsadfsdf"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:24:04] local.INFO: Message send okay with408d2d88-cd87-47e5-ae1b-f3cb760ac36f  
[2025-05-30 17:27:14] local.INFO: data is {"task_id":69,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:27:13","send_to_number":"9510991141","templateId":"19","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"2af58c47-964e-46b4-beb1-4463c7694d83","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:27:14] local.DEBUG: {"message":"OK","referenceID":"70719a47-fad8-42cd-8854-07748f2dcad9"}  
[2025-05-30 17:27:14] local.DEBUG: Message Data: {"messageID":"2af58c47-964e-46b4-beb1-4463c7694d83","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"asdfsdafsadfsadf","suggestions":[{"action":{"plainText":"call on","postBack":{"data":"dial_suggestion_1{{$50}}"},"dialerAction":{"phoneNumber":"+************"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:27:14] local.INFO: Message send okay with70719a47-fad8-42cd-8854-07748f2dcad9  
[2025-05-30 17:28:05] local.INFO: data is {"task_id":70,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:28:03","send_to_number":"9510991141","templateId":"20","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"c21b5fad-c271-4804-9366-2c9e0627d3d1","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:28:06] local.DEBUG: {"message":"OK","referenceID":"25031ec7-c2e9-4a29-8bb2-81a8085e33c3"}  
[2025-05-30 17:28:06] local.DEBUG: Message Data: {"messageID":"c21b5fad-c271-4804-9366-2c9e0627d3d1","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"asdfsdafsadfsadf","suggestions":[{"action":{"plainText":"call on","postBack":{"data":"dial_suggestion_1{{$50}}"},"dialerAction":{"phoneNumber":"+************"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_2{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.1689,"longitude":0},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:28:06] local.INFO: Message send okay with25031ec7-c2e9-4a29-8bb2-81a8085e33c3  
[2025-05-30 17:29:18] local.INFO: data is {"task_id":71,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:29:15","send_to_number":"9510991141","templateId":"21","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"cac5a64e-7bfa-40d5-b0cd-31933482b440","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:29:19] local.DEBUG: {"message":"OK","referenceID":"4c196fa6-b1ff-4d8d-bb8c-a707be4643cf"}  
[2025-05-30 17:29:19] local.DEBUG: Message Data: {"messageID":"cac5a64e-7bfa-40d5-b0cd-31933482b440","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"sadsadfasdfsadfsd","suggestions":[{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":12.121212,"longitude":12.121212},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:29:19] local.INFO: Message send okay with4c196fa6-b1ff-4d8d-bb8c-a707be4643cf  
[2025-05-30 17:30:07] local.INFO: data is {"task_id":72,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:30:06","send_to_number":"9510991141","templateId":"23","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"907051c8-a771-4091-b20a-66d045d0dea1","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:30:08] local.DEBUG: {"message":"OK","referenceID":"4161bac7-49a5-4224-bb9f-44d36eab2210"}  
[2025-05-30 17:30:08] local.DEBUG: Message Data: {"messageID":"907051c8-a771-4091-b20a-66d045d0dea1","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"sadsadfasdfsadfsd","suggestions":[{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":12.121212,"longitude":12.121212},"label":"Open Map"}}},{"reply":{"plainText":"Hiiii","postBack":{"data":"reply_suggestion_2{{$50}}"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:30:08] local.INFO: Message send okay with4161bac7-49a5-4224-bb9f-44d36eab2210  
[2025-05-30 17:32:26] local.INFO: data is {"task_id":73,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:32:24","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a4dbab62-b9c9-4282-b664-515214bd76b2","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:32:27] local.DEBUG: {"message":"OK","referenceID":"dd05220e-df67-40eb-a793-ca9fd29fd505"}  
[2025-05-30 17:32:27] local.DEBUG: Message Data: {"messageID":"a4dbab62-b9c9-4282-b664-515214bd76b2","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"reply":{"plainText":"Hiii","postBack":{"data":"reply_suggestion_1{{$50}}"}}},{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}},{"action":{"plainText":"Visit Now","postBack":{"data":"url_suggestion_4{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:32:27] local.INFO: Message send okay withdd05220e-df67-40eb-a793-ca9fd29fd505  
[2025-05-30 17:35:07] local.INFO: data is {"task_id":74,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:35:03","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"d6252256-119c-4399-bb5f-b87b4e070eb6","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:35:07] local.DEBUG: {"message":"OK","referenceID":"5cb57ab4-0445-4489-8658-d5e9eb4cd5a3"}  
[2025-05-30 17:35:07] local.DEBUG: Message Data: {"messageID":"d6252256-119c-4399-bb5f-b87b4e070eb6","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"reply":{"plainText":"Hiii","postBack":{"data":"reply_suggestion_1{{$50}}"}}},{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:35:07] local.INFO: Message send okay with5cb57ab4-0445-4489-8658-d5e9eb4cd5a3  
[2025-05-30 17:36:28] local.INFO: data is {"task_id":75,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:36:25","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"47d2d729-4d36-4aad-bf97-4df318e146fa","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:36:29] local.DEBUG: {"message":"OK","referenceID":"cdf17cc3-2c86-4bce-a6ca-fdf2e966a7fb"}  
[2025-05-30 17:36:29] local.DEBUG: Message Data: {"messageID":"47d2d729-4d36-4aad-bf97-4df318e146fa","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:36:29] local.INFO: Message send okay withcdf17cc3-2c86-4bce-a6ca-fdf2e966a7fb  
[2025-05-30 17:38:11] local.INFO: data is {"task_id":76,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:38:09","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"fb3a0f6f-eecc-499b-b141-fed7f0e517af","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:38:11] local.DEBUG: {"message":"OK","referenceID":"1e36f35b-8fd4-40cc-a936-deac05a64bf2"}  
[2025-05-30 17:38:11] local.DEBUG: Message Data: {"messageID":"fb3a0f6f-eecc-499b-b141-fed7f0e517af","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"aaddffasdfasdfdsafdsfdsf","suggestions":[{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.212121,"longitude":21.12121},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:38:11] local.INFO: Message send okay with1e36f35b-8fd4-40cc-a936-deac05a64bf2  
[2025-05-30 17:39:43] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a007fd3e0bde1953b9a1\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748606982311.jpeg\",\"status\":\"success\"}","file_name":"download.jpeg","file_size":3428,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:40:51] local.INFO: data is {"task_id":77,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:40:50","send_to_number":"9510991141","templateId":"3","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"c725a3cd-1ce9-47e1-87e4-fd251c6ed7bc","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:40:51] local.DEBUG: {"message":"OK","referenceID":"48ad1f69-82ff-4ff6-9dff-6afec15aac8e"}  
[2025-05-30 17:40:51] local.DEBUG: Message Data: {"messageID":"c725a3cd-1ce9-47e1-87e4-fd251c6ed7bc","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Plan Text With Image Video"},"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748606982311.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748606983.jpg"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:40:51] local.INFO: Message send okay with48ad1f69-82ff-4ff6-9dff-6afec15aac8e  
[2025-05-30 17:42:51] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a0c352c012d110c2c802\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607171398.png\",\"status\":\"success\"}","file_name":"download (3).png","file_size":3460,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:43:13] local.INFO: data is {"task_id":78,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:43:10","send_to_number":"9510991141","templateId":"4","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9c8b7eac-87ad-4516-a89f-fb44c72f9ea9","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:43:14] local.DEBUG: {"message":"OK","referenceID":"d3e79f1e-b532-4100-80c7-639c5b60ba1f"}  
[2025-05-30 17:43:14] local.DEBUG: Message Data: {"messageID":"9c8b7eac-87ad-4516-a89f-fb44c72f9ea9","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"test card title","cardDescription":"Card Descriptions","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607171398.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607171398.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607171.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiii","postBack":{"data":"reply_suggestion_1"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:43:14] local.INFO: Message send okay withd3e79f1e-b532-4100-80c7-639c5b60ba1f  
[2025-05-30 17:43:35] local.INFO: data is {"task_id":79,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:43:34","send_to_number":"9510991141","templateId":"4","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"bbc2524d-8188-47ea-bdbf-595753281326","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:43:35] local.DEBUG: {"message":"OK","referenceID":"0e518aee-7109-4edb-b1df-a4ed203323b4"}  
[2025-05-30 17:43:35] local.DEBUG: Message Data: {"messageID":"bbc2524d-8188-47ea-bdbf-595753281326","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"test card title","cardDescription":"Card Descriptions","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607171398.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607171398.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607171.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiii","postBack":{"data":"reply_suggestion_1"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:43:35] local.INFO: Message send okay with0e518aee-7109-4edb-b1df-a4ed203323b4  
[2025-05-30 17:44:39] local.INFO: data is {"task_id":80,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:44:39","send_to_number":"9510991141","templateId":"4","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"c9583b1a-1836-44c7-aa0d-f41c6ec71a55","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:44:40] local.DEBUG: {"message":"OK","referenceID":"16e33432-aea4-44d9-a240-005557d68001"}  
[2025-05-30 17:44:40] local.DEBUG: Message Data: {"messageID":"c9583b1a-1836-44c7-aa0d-f41c6ec71a55","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"test card title","cardDescription":"Card Descriptions","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607171398.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607171398.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607171.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiii","postBack":{"data":"reply_suggestion_1{{$50}}"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:44:40] local.INFO: Message send okay with16e33432-aea4-44d9-a240-005557d68001  
[2025-05-30 17:47:19] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a1cfdf5da71f693b6d6f\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607438345.jpeg\",\"status\":\"success\"}","file_name":"download.jpeg","file_size":3428,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:47:32] local.INFO: data is {"task_id":81,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:47:30","send_to_number":"9510991141","templateId":"5","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"1192e91f-7f49-4d3c-92af-120b14e4a11f","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:47:32] local.DEBUG: {"message":"OK","referenceID":"d45646e5-cf60-4544-821d-7a879afa28d1"}  
[2025-05-30 17:47:32] local.DEBUG: Message Data: {"messageID":"1192e91f-7f49-4d3c-92af-120b14e4a11f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"rich card title test","cardDescription":"rich card description test","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607438345.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607438345.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607439.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hellow","postBack":{"data":"reply_suggestion_1{{$50}}"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:47:32] local.INFO: Message send okay withd45646e5-cf60-4544-821d-7a879afa28d1  
[2025-05-30 17:48:00] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a1f8a7a741d75ed0340f\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607479281.jpeg\",\"status\":\"success\"}","file_name":"download.jpeg","file_size":3428,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:48:11] local.INFO: data is {"task_id":82,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:48:11","send_to_number":"9510991141","templateId":"6","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"ae10eece-d2f0-447c-a9c9-276368a09a93","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:48:12] local.DEBUG: {"message":"OK","referenceID":"1ecfdcbb-dc6e-405b-a40c-7c9a428c0ca5"}  
[2025-05-30 17:48:12] local.DEBUG: Message Data: {"messageID":"ae10eece-d2f0-447c-a9c9-276368a09a93","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"rich card title test","cardDescription":"rich card description test","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607479281.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607479281.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607480.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hellow","postBack":{"data":"reply_suggestion_1{{$50}}"}}},{"action":{"plainText":"Call On","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+91 8320677031"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:48:12] local.INFO: Message send okay with1ecfdcbb-dc6e-405b-a40c-7c9a428c0ca5  
[2025-05-30 17:48:30] local.INFO: data is {"task_id":83,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:48:27","send_to_number":"9510991141","templateId":"6","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"f96efea7-8768-43d0-bc2a-3f7f2d1a8468","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:48:30] local.DEBUG: {"message":"OK","referenceID":"41166989-f6c9-45ea-b18a-b1a2b5f0091c"}  
[2025-05-30 17:48:30] local.DEBUG: Message Data: {"messageID":"f96efea7-8768-43d0-bc2a-3f7f2d1a8468","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"rich card title test","cardDescription":"rich card description test","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607479281.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607479281.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607480.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hellow","postBack":{"data":"reply_suggestion_1{{$50}}"}}},{"action":{"plainText":"Call On","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+91 8320677031"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:48:30] local.INFO: Message send okay with41166989-f6c9-45ea-b18a-b1a2b5f0091c  
[2025-05-30 17:48:59] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a23322f52b083ee240c7\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607539726.jpeg\",\"status\":\"success\"}","file_name":"download.jpeg","file_size":3428,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:49:19] local.INFO: data is {"task_id":84,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:49:17","send_to_number":"9510991141","templateId":"7","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"cdff2b1b-c424-4bbd-8d23-4c22962648a9","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:49:19] local.DEBUG: {"message":"OK","referenceID":"a322cf26-8a3f-45d2-984a-0583d4dc2da9"}  
[2025-05-30 17:49:19] local.DEBUG: Message Data: {"messageID":"cdff2b1b-c424-4bbd-8d23-4c22962648a9","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"rich card title test","cardDescription":"rich card description test","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607539726.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607539726.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607539.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:49:19] local.INFO: Message send okay witha322cf26-8a3f-45d2-984a-0583d4dc2da9  
[2025-05-30 17:51:10] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a2b6f1177625cd87b1ac\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607669456.png\",\"status\":\"success\"}","file_name":"download (4).png","file_size":3219,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:51:11] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a2b73548385ccab04621\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607670592.png\",\"status\":\"success\"}","file_name":"echarts (2).png","file_size":26832,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:51:38] local.INFO: data is {"task_id":85,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:51:35","send_to_number":"9510991141","templateId":"8","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"038ccada-d277-427f-8964-cb3afcc7b0db","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:51:39] local.DEBUG: {"message":"OK","referenceID":"981b2fbb-ec90-4def-bfca-03aa15925f56"}  
[2025-05-30 17:51:39] local.DEBUG: Message Data: {"messageID":"038ccada-d277-427f-8964-cb3afcc7b0db","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"SMALL","contents":[{"cardTitle":"rich card 1","cardDescription":"rich card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607669456.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607669456.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607670.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Call On","postBack":{"data":"card1_dial_suggestion_1{{$50}}"},"dialerAction":{"phoneNumber":"+************"}}}]},{"cardTitle":"Rich Card 2 titile","cardDescription":"Rich card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607670592.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607670592.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607671.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:51:39] local.INFO: Message send okay with981b2fbb-ec90-4def-bfca-03aa15925f56  
[2025-05-30 17:52:09] local.INFO: data is {"task_id":86,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:52:06","send_to_number":"9510991141","templateId":"8","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"5ea94d1a-a82a-44c7-aab5-db484f4c5957","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:52:09] local.DEBUG: {"message":"OK","referenceID":"a120dd13-e52e-43e6-8ee1-380e28b39f52"}  
[2025-05-30 17:52:09] local.DEBUG: Message Data: {"messageID":"5ea94d1a-a82a-44c7-aab5-db484f4c5957","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"SMALL","contents":[{"cardTitle":"rich card 1","cardDescription":"rich card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607669456.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607669456.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607670.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Call On","postBack":{"data":"card1_dial_suggestion_1{{$50}}"},"dialerAction":{"phoneNumber":"+************"}}}]},{"cardTitle":"Rich Card 2 titile","cardDescription":"Rich card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607670592.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607670592.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607671.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:52:09] local.INFO: Message send okay witha120dd13-e52e-43e6-8ee1-380e28b39f52  
[2025-05-30 17:52:31] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a30718ebcd1f4bf81f32\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607751975.png\",\"status\":\"success\"}","file_name":"download (4).png","file_size":3219,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:52:32] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a30869ff85bfc8c1598d\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607752857.png\",\"status\":\"success\"}","file_name":"echarts (2).png","file_size":26832,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:52:42] local.INFO: data is {"task_id":87,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:52:42","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"61475ecf-7d46-4d72-8eed-dbf27b1738ec","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:52:43] local.DEBUG: {"message":"OK","referenceID":"07e71a67-38d9-4cff-ae13-86565ebbab19"}  
[2025-05-30 17:52:43] local.DEBUG: Message Data: {"messageID":"61475ecf-7d46-4d72-8eed-dbf27b1738ec","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"SMALL","contents":[{"cardTitle":"rich card 1","cardDescription":"rich card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607751975.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607751975.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607751.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},{"cardTitle":"Rich Card 2 titile","cardDescription":"Rich card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607752857.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607752857.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607752.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:52:43] local.INFO: Message send okay with07e71a67-38d9-4cff-ae13-86565ebbab19  
[2025-05-30 17:54:16] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a3706f5770e8641f88fb\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607855678.png\",\"status\":\"success\"}","file_name":"download (4).png","file_size":3219,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:54:16] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a37011de65d78a8cb827\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607856683.png\",\"status\":\"success\"}","file_name":"echarts (2).png","file_size":26832,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:54:17] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a3711c881588977a234f\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607857995.jpeg\",\"status\":\"success\"}","file_name":"WhatsApp Image 2025-02-24 at 9.44.41 AM.jpeg","file_size":114361,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:54:18] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a372fc1fac57f440ae1a\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748607858439.png\",\"status\":\"success\"}","file_name":"Untitled 1.png","file_size":48006,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:54:28] local.INFO: data is {"task_id":88,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:54:28","send_to_number":"9510991141","templateId":"10","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"e537440e-13e5-4582-9e6a-cbb360bae452","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:54:29] local.DEBUG: {"message":"OK","referenceID":"64f3ead1-4823-43c3-99b4-10b748c7c095"}  
[2025-05-30 17:54:29] local.DEBUG: Message Data: {"messageID":"e537440e-13e5-4582-9e6a-cbb360bae452","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"SMALL","contents":[{"cardTitle":"rich card 1","cardDescription":"rich card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607855678.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607855678.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607856.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hellow","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"Rich Card 2 titile","cardDescription":"Rich card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607856683.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607856683.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607856.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiii","postBack":{"data":"card2_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"Card 3 title","cardDescription":"card 3 descreiption","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607857995.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607857995.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607857.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Testing","postBack":{"data":"card3_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"card 4 title","cardDescription":"card 4 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607858439.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607858439.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748607858.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"C 3 Reply","postBack":{"data":"card4_reply_suggestion_1{{$50}}"}}}]}]}}},"_carousel_meta":{"total_cards":4,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:54:29] local.INFO: Message send okay with64f3ead1-4823-43c3-99b4-10b748c7c095  
[2025-05-30 17:58:53] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a48518ebcd1f4bf81f33\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748608133277.png\",\"status\":\"success\"}","file_name":"AC-DC.png","file_size":50310,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:58:54] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a4862a4f5dbdfec0625c\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748608134816.png\",\"status\":\"success\"}","file_name":"Type-1 (1).png","file_size":50310,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 17:59:09] local.INFO: data is {"task_id":89,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 17:59:09","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"87d04e6b-ca86-40ca-977d-7d4329f21db7","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 17:59:10] local.DEBUG: {"message":"OK","referenceID":"00c759db-6616-4bc4-ba97-24594f18d583"}  
[2025-05-30 17:59:10] local.DEBUG: Message Data: {"messageID":"87d04e6b-ca86-40ca-977d-7d4329f21db7","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"SMALL","contents":[{"cardTitle":"card 1","cardDescription":"card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748608133277.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748608133277.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748608133.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]},{"cardTitle":"card 2 title","cardDescription":"card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748608134816.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748608134816.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748608134.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 17:59:10] local.INFO: Message send okay with00c759db-6616-4bc4-ba97-24594f18d583  
[2025-05-30 18:05:03] local.INFO: data is {"task_id":90,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:05:01","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"6a1e888d-4fbc-41e6-ace0-a3fece2df95b","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:05:03] local.DEBUG: {"message":"OK","referenceID":"b21c7ef5-471f-4efb-8cdb-ddf756861d87"}  
[2025-05-30 18:05:03] local.DEBUG: Message Data: {"messageID":"6a1e888d-4fbc-41e6-ace0-a3fece2df95b","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"messageID":"87d04e6b-ca86-40ca-977d-7d4329f21db7","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"SMALL","contents":[{"cardTitle":"card 1","cardDescription":"card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748608133277.png"}},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]},{"cardTitle":"card 2 title","cardDescription":"card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748608134816.png"}},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]}]}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:05:03] local.INFO: Message send okay withb21c7ef5-471f-4efb-8cdb-ddf756861d87  
[2025-05-30 18:07:46] local.INFO: data is {"task_id":91,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:07:45","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"3cc66b85-94e7-490f-bec0-c557c682e31b","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:07:46] local.DEBUG: {"message":"OK","referenceID":"6b5659a0-2fe3-4356-811a-441034440f41"}  
[2025-05-30 18:07:46] local.DEBUG: Message Data: {"messageID":"3cc66b85-94e7-490f-bec0-c557c682e31b","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"SMALL","contents":[{"cardTitle":"rich card 1","cardDescription":"rich card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607855678.png"}},"suggestions":[{"reply":{"plainText":"Hellow","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"Rich Card 2 titile","cardDescription":"Rich card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607856683.png"}},"suggestions":[{"reply":{"plainText":"Hiii","postBack":{"data":"card2_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"Card 3 title","cardDescription":"card 3 descreiption","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607857995.jpeg"}},"suggestions":[{"reply":{"plainText":"Testing","postBack":{"data":"card3_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"card 4 title","cardDescription":"card 4 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607858439.png"}},"suggestions":[{"reply":{"plainText":"C 3 Reply","postBack":{"data":"card4_reply_suggestion_1{{$50}}"}}}]}]}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:07:46] local.INFO: Message send okay with6b5659a0-2fe3-4356-811a-441034440f41  
[2025-05-30 18:09:41] local.INFO: data is {"task_id":92,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:09:39","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"d2dbfdbf-56c3-48a3-89eb-205522291524","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:09:42] local.DEBUG: {"message":"OK","referenceID":"a2a121c9-4814-4d88-af02-ce5d952f4603"}  
[2025-05-30 18:09:42] local.DEBUG: Message Data: {"messageID":"d2dbfdbf-56c3-48a3-89eb-205522291524","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"messageID":"3cc66b85-94e7-490f-bec0-c557c682e31b","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"rich card 1","cardDescription":"rich card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607855678.png"}},"suggestions":[{"reply":{"plainText":"Hellow","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"Rich Card 2 titile","cardDescription":"Rich card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607856683.png"}},"suggestions":[{"reply":{"plainText":"Hiii","postBack":{"data":"card2_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"Card 3 title","cardDescription":"card 3 descreiption","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607857995.jpeg"}},"suggestions":[{"reply":{"plainText":"Testing","postBack":{"data":"card3_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"card 4 title","cardDescription":"card 4 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748607858439.png"}},"suggestions":[{"reply":{"plainText":"C 3 Reply","postBack":{"data":"card4_reply_suggestion_1{{$50}}"}}}]}]}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:09:42] local.INFO: Message send okay witha2a121c9-4814-4d88-af02-ce5d952f4603  
[2025-05-30 18:17:49] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a8f511de65d78a8cb828\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748609267655.png\",\"status\":\"success\"}","file_name":"download (4).png","file_size":3219,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 18:17:50] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839a8f6bfbcab87dc7e7cfd\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748609270415.png\",\"status\":\"success\"}","file_name":"download (3).png","file_size":3460,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 18:18:20] local.INFO: data is {"task_id":93,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:18:17","send_to_number":"9510991141","templateId":"12","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"b3497560-9276-451f-b0b1-97a40ed5ad96","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:18:21] local.DEBUG: {"message":"OK","referenceID":"9ad88f4d-cba2-4775-a57d-17c26741bb49"}  
[2025-05-30 18:18:21] local.DEBUG: Message Data: {"messageID":"b3497560-9276-451f-b0b1-97a40ed5ad96","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM","contents":[{"cardTitle":"card 1 title","cardDescription":"card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609269.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]},{"cardTitle":"car 2 title","cardDescription":"card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609270.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Open","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:18:21] local.INFO: Message send okay with9ad88f4d-cba2-4775-a57d-17c26741bb49  
[2025-05-30 18:20:21] local.INFO: data is {"task_id":94,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:20:20","send_to_number":"9510991141","templateId":"12","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"aa6ea256-0b4b-4d56-b5fc-2f12d0d9dd55","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:20:22] local.DEBUG: {"message":"OK","referenceID":"0f7f14c4-e4d1-4772-bb32-ebb1efd32a40"}  
[2025-05-30 18:20:22] local.DEBUG: Message Data: {"messageID":"aa6ea256-0b4b-4d56-b5fc-2f12d0d9dd55","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"card 1 title","cardDescription":"card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609269.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]},{"cardTitle":"car 2 title","cardDescription":"card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609270.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Open","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:20:22] local.INFO: Message send okay with0f7f14c4-e4d1-4772-bb32-ebb1efd32a40  
[2025-05-30 18:21:10] local.INFO: data is {"task_id":95,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:21:10","send_to_number":"9510991141","templateId":"12","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a6ba8165-e91f-4634-b585-aed30a117a00","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:21:11] local.DEBUG: {"message":"OK","referenceID":"0e7bf46a-a880-4e09-988a-a734a581e101"}  
[2025-05-30 18:21:11] local.DEBUG: Message Data: {"messageID":"a6ba8165-e91f-4634-b585-aed30a117a00","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM","contents":[{"cardTitle":"card 1 title","cardDescription":"card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609269.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]},{"cardTitle":"car 2 title","cardDescription":"card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609270.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Open","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:21:11] local.INFO: Message send okay with0e7bf46a-a880-4e09-988a-a734a581e101  
[2025-05-30 18:21:53] local.INFO: data is {"task_id":96,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:21:52","send_to_number":"9510991141","templateId":"12","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"05eb1629-e722-4507-9bf8-dd97ea783026","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:21:53] local.DEBUG: {"message":"OK","referenceID":"03443c7a-62ec-4575-b7e3-588dc4c95110"}  
[2025-05-30 18:21:53] local.DEBUG: Message Data: {"messageID":"05eb1629-e722-4507-9bf8-dd97ea783026","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"card 1 title","cardDescription":"card 1 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609267655.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609269.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]},{"cardTitle":"car 2 title","cardDescription":"card 2 description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609270415.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609270.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Open","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/google.com"}}}]}]}}},"_carousel_meta":{"total_cards":2,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:21:53] local.INFO: Message send okay with03443c7a-62ec-4575-b7e3-588dc4c95110  
[2025-05-30 18:24:40] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839aa904e1cbc00bb83ce61\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748609680867.png\",\"status\":\"success\"}","file_name":"download (4).png","file_size":3219,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 18:24:41] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839aa91b9a858902207857c\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/174860968173.png\",\"status\":\"success\"}","file_name":"AC-DC.png","file_size":50310,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 18:24:42] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"6839aa9269ff85bfc8c1598e\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748609682177.jpeg\",\"status\":\"success\"}","file_name":"WhatsApp Image 2025-02-24 at 9.44.41 AM.jpeg","file_size":114361,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 18:25:03] local.INFO: data is {"task_id":97,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:25:00","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9b4e8ce5-8738-45be-8a6e-08fbc03703ee","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:25:04] local.DEBUG: {"message":"OK","referenceID":"784b36dd-6aac-45a9-8799-24680b112baa"}  
[2025-05-30 18:25:04] local.DEBUG: Message Data: {"messageID":"9b4e8ce5-8738-45be-8a6e-08fbc03703ee","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"asdf","cardDescription":"asdfasdfsdaf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609680.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiiii","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"asdfasdf","cardDescription":"sadfsadfsadfsadf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609681.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},{"cardTitle":"sadfsdaf","cardDescription":"sadfcwer3cdef","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609682.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card3_location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.21212,"longitude":12.21212},"label":"Visit"}}}]}]}}},"_carousel_meta":{"total_cards":3,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:25:04] local.INFO: Message send okay with784b36dd-6aac-45a9-8799-24680b112baa  
[2025-05-30 18:26:56] local.INFO: data is {"task_id":98,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:26:55","send_to_number":"919998797232","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"73c42f6a-8b56-44fc-8e7b-f2caaf9a12de","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:26:56] local.DEBUG: {"message":"OK","referenceID":"3e3c3107-b97e-4243-98a9-c60c2dacd4a8"}  
[2025-05-30 18:26:56] local.DEBUG: Message Data: {"messageID":"73c42f6a-8b56-44fc-8e7b-f2caaf9a12de","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919998797232"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"asdf","cardDescription":"asdfasdfsdaf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609680.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiiii","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"asdfasdf","cardDescription":"sadfsadfsadfsadf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609681.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},{"cardTitle":"sadfsdaf","cardDescription":"sadfcwer3cdef","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609682.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card3_location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.21212,"longitude":12.21212},"label":"Visit"}}}]}]}}},"_carousel_meta":{"total_cards":3,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:26:56] local.INFO: Message send okay with3e3c3107-b97e-4243-98a9-c60c2dacd4a8  
[2025-05-30 18:27:29] local.INFO: data is {"task_id":99,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:27:28","send_to_number":"9998797232","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"dd50df7e-643a-4ff5-9ec4-d245b828445a","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:27:30] local.DEBUG: {"message":"OK","referenceID":"5dd9b72b-eb7b-45d9-97c5-dccda8b4da2a"}  
[2025-05-30 18:27:30] local.DEBUG: Message Data: {"messageID":"dd50df7e-643a-4ff5-9ec4-d245b828445a","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9998797232"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"asdf","cardDescription":"asdfasdfsdaf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609680.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiiii","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"asdfasdf","cardDescription":"sadfsadfsadfsadf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609681.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},{"cardTitle":"sadfsdaf","cardDescription":"sadfcwer3cdef","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609682.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card3_location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.21212,"longitude":12.21212},"label":"Visit"}}}]}]}}},"_carousel_meta":{"total_cards":3,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:27:30] local.INFO: Message send okay with5dd9b72b-eb7b-45d9-97c5-dccda8b4da2a  
[2025-05-30 18:27:30] local.INFO: data is {"task_id":100,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 18:27:28","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"eb42ab51-c50c-40e9-b132-1266775fed4f","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 18:27:30] local.DEBUG: {"message":"OK","referenceID":"53baf74f-14a4-4fa1-bd62-e29d903146e3"}  
[2025-05-30 18:27:30] local.DEBUG: Message Data: {"messageID":"eb42ab51-c50c-40e9-b132-1266775fed4f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"asdf","cardDescription":"asdfasdfsdaf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609680.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiiii","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"asdfasdf","cardDescription":"sadfsadfsadfsadf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609681.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},{"cardTitle":"sadfsdaf","cardDescription":"sadfcwer3cdef","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609682.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card3_location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.21212,"longitude":12.21212},"label":"Visit"}}}]}]}}},"_carousel_meta":{"total_cards":3,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 18:27:30] local.INFO: Message send okay with53baf74f-14a4-4fa1-bd62-e29d903146e3  
[2025-05-31 09:55:21] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"683a84b2919f33523853c658\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748665521683.png\",\"status\":\"success\"}","file_name":"qc_logo.png","file_size":4945,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-31 10:02:01] local.INFO: data is {"task_id":101,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-31 10:01:33","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"841f5257-e196-42d0-9662-3bad77ff218b","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-31 10:02:01] local.DEBUG: {"message":"OK","referenceID":"d2acd8ef-aab1-4339-ae6c-b6797375bac5"}  
[2025-05-31 10:02:01] local.DEBUG: Message Data: {"messageID":"841f5257-e196-42d0-9662-3bad77ff218b","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-31 10:02:01] local.INFO: Message send okay withd2acd8ef-aab1-4339-ae6c-b6797375bac5  
[2025-05-31 10:02:31] local.INFO: data is {"task_id":102,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-31 10:02:31","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a59ae2a8-0524-4464-bdc2-a12642a2591d","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-31 10:02:32] local.DEBUG: {"message":"OK","referenceID":"4dcc28f0-8c06-4f1d-a735-cab7f0c3c141"}  
[2025-05-31 10:02:32] local.DEBUG: Message Data: {"messageID":"a59ae2a8-0524-4464-bdc2-a12642a2591d","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"aaddffasdfasdfdsafdsfdsf","suggestions":[{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.212121,"longitude":21.12121},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-31 10:02:32] local.INFO: Message send okay with4dcc28f0-8c06-4f1d-a735-cab7f0c3c141  
[2025-05-31 10:02:32] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 {main}
"} 
[2025-05-31 10:02:32] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(255): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#19 {main}
"} 
[2025-05-31 10:03:56] local.INFO: data is {"task_id":103,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-31 10:03:50","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"ff3b2b49-4f4c-4ab3-8664-ce4d11aff9e2","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-31 10:03:57] local.DEBUG: {"message":"OK","referenceID":"3a898637-76d0-46a5-ab3c-dfd521dde897"}  
[2025-05-31 10:03:57] local.DEBUG: Message Data: {"messageID":"ff3b2b49-4f4c-4ab3-8664-ce4d11aff9e2","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"aaddffasdfasdfdsafdsfdsf","suggestions":[{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.212121,"longitude":21.12121},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-31 10:03:57] local.INFO: Message send okay with3a898637-76d0-46a5-ab3c-dfd521dde897  
[2025-05-31 10:04:34] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 {main}
"} 
[2025-05-31 10:04:34] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(957): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(942): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(255): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#19 {main}
"} 
[2025-05-31 10:04:36] local.INFO: data is {"task_id":104,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-31 10:04:33","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"b0ff28ec-97c7-4b31-a351-a64d7876cf15","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-31 10:04:37] local.DEBUG: {"message":"OK","referenceID":"cac77766-0f72-4b08-84ef-90e1a00b062b"}  
[2025-05-31 10:04:37] local.DEBUG: Message Data: {"messageID":"b0ff28ec-97c7-4b31-a351-a64d7876cf15","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-31 10:04:37] local.INFO: Message send okay withcac77766-0f72-4b08-84ef-90e1a00b062b  
[2025-05-31 10:05:37] local.INFO: data is {"task_id":105,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-31 10:05:37","send_to_number":"9510991141","templateId":"14","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"b62ffab5-8ee0-470d-8668-586e40469b9e","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-31 10:05:38] local.DEBUG: {"message":"OK","referenceID":"f7201379-d58d-42d2-8eca-2c642776570b"}  
[2025-05-31 10:05:38] local.DEBUG: Message Data: {"messageID":"b62ffab5-8ee0-470d-8668-586e40469b9e","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"rich card title","cardDescription":"rich card description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748665521683.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748665521683.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748665521.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hellow","postBack":{"data":"reply_suggestion_1{{$50}}"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-31 10:05:38] local.INFO: Message send okay withf7201379-d58d-42d2-8eca-2c642776570b  
[2025-05-31 10:11:49] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"683a888ebebf5e2d68907ed3\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748666510948.png\",\"status\":\"success\"}","file_name":"nxc.png","file_size":25025,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-31 10:11:50] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"683a888fa6c86b3b11953b13\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748666511225.jpeg\",\"status\":\"success\"}","file_name":"SCZBG25.jpg","file_size":46564,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-31 10:11:55] local.INFO: RCS File Upload Response {"status":400,"body":"{\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","file_name":"mumbai-skyline-skyscrapers-construction.jpg","file_size":11612027,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-31 10:12:01] local.INFO: RCS File Upload Response {"status":400,"body":"{\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","file_name":"analog-landscape-city-with-buildings.jpg","file_size":17139679,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-31 10:12:31] local.INFO: data is {"task_id":106,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-31 10:12:30","send_to_number":"9510991141","templateId":"15","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"fd72937b-e408-4170-8c90-1209cf11efa9","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-31 10:12:31] local.DEBUG: {"message":"OK","referenceID":"fd97d53b-8452-42e9-8ad9-b97be5a70422"}  
[2025-05-31 10:12:31] local.DEBUG: Message Data: {"messageID":"fd72937b-e408-4170-8c90-1209cf11efa9","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"RCS Infobip Testing","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/nf\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666510948.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666510948.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666509.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]},{"cardTitle":"RCS Apigee testing 2","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/n\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666511225.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666511225.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666510.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]},{"cardTitle":"RCS Apigee testing 3","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/n\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666515.jpg"}},"_meta":{"rcs_media_url":null,"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666515.jpg","upload_error":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card3_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]},{"cardTitle":"RCS Apigee testing 4","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/n\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666521.jpg"}},"_meta":{"rcs_media_url":null,"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666521.jpg","upload_error":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card4_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]}]}}},"_carousel_meta":{"total_cards":4,"upload_errors":{"card3":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","card4":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}"},"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-31 10:12:31] local.INFO: Message send okay withfd97d53b-8452-42e9-8ad9-b97be5a70422  
[2025-05-31 10:14:23] local.INFO: data is {"task_id":107,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-31 10:14:22","send_to_number":"9510991141","templateId":"15","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"533b0f0e-ba34-4c3e-98d9-ab46edff9857","task_type":"text","id":1,"phone":"************","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-31 10:14:23] local.DEBUG: {"message":"OK","referenceID":"4d6952dc-eacf-406c-bf71-ea00ea44eb96"}  
[2025-05-31 10:14:23] local.DEBUG: Message Data: {"messageID":"533b0f0e-ba34-4c3e-98d9-ab46edff9857","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"RCS Infobip Testing","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/nf\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666510948.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666510948.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666509.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card1_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]},{"cardTitle":"RCS Apigee testing 2","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/n\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666511225.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666511225.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666510.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card2_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]},{"cardTitle":"RCS Apigee testing 3","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/n\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666510948.png"}},"_meta":{"rcs_media_url":null,"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666515.jpg","upload_error":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card3_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]},{"cardTitle":"RCS Apigee testing 4","cardDescription":"Refill Booking no confirmed: 1234567 \/n \/n Paying for your LPG Cylinder is now quick, easy and hassle free!!\/n\/n To make instant payment on Amazon Pay, Click on 'Pay Now' below\/n\/n Your Cylinder will be delivered soon.","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748666510948.png"}},"_meta":{"rcs_media_url":null,"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748666521.jpg","upload_error":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"card4_url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/www.ioclmd.in\/"}}}]}]}}},"_carousel_meta":{"total_cards":4,"upload_errors":{"card3":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}","card4":"File upload failed: {\"error\":\"Media Size is above limit. Please make sure it is below the accepted limit\"}"},"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-31 10:14:23] local.INFO: Message send okay with4d6952dc-eacf-406c-bf71-ea00ea44eb96  
[2025-05-31 10:18:28] local.ERROR: Missing required parameter for [Route: user.template.edit] [URI: user/template/{template}/edit] [Missing parameter: template]. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\resources\\views\\user\\template\\view.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1827933785 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1958</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1827933785\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","device":"<pre class=sf-dump id=sf-dump-341841380 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Device</span> {<a class=sf-dump-ref>#1999</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">devices</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>uuid</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5a568965-12af-4eb2-b2eb-************</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NXC Controls</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"
    \"<span class=sf-dump-key>user_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>qr</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>meta</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>waid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>phoneid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>msg_limit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>quality_rating</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0b6345e5-710a-438f-af94-fd810d025250</span>\"
    \"<span class=sf-dump-key>t_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>t_bot_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>auto_send_mkt_fail_msg</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>typebot</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>uuid</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5a568965-12af-4eb2-b2eb-************</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NXC Controls</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"
    \"<span class=sf-dump-key>user_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>qr</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>meta</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>waid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>phoneid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>msg_limit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>quality_rating</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0b6345e5-710a-438f-af94-fd810d025250</span>\"
    \"<span class=sf-dump-key>t_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>t_bot_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>auto_send_mkt_fail_msg</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>typebot</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">uuid</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-341841380\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","templates":"<pre class=sf-dump id=sf-dump-2115885066 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2008</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2010</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2011</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2012</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2013</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>4</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2014</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>5</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2015</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>6</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2016</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-2115885066\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","total_templates":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>7</span>
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Missing required parameter for [Route: user.template.edit] [URI: user/template/{template}/edit] [Missing parameter: template]. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Exceptions\\UrlGenerationException.php:35)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php(90): Illuminate\\Routing\\Exceptions\\UrlGenerationException::forMissingParameters(Object(Illuminate\\Routing\\Route), Array)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(500): Illuminate\\Routing\\RouteUrlGenerator->to(Object(Illuminate\\Routing\\Route), Array, true)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(469): Illuminate\\Routing\\UrlGenerator->toRoute(Object(Illuminate\\Routing\\Route), Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('user.template.e...', Array, true)
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\resources\\views\\user\\template\\view.blade.php(35): route('user.template.e...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}

[previous exception] [object] (Illuminate\\Routing\\Exceptions\\UrlGenerationException(code: 0): Missing required parameter for [Route: user.template.edit] [URI: user/template/{template}/edit] [Missing parameter: template]. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Exceptions\\UrlGenerationException.php:35)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php(90): Illuminate\\Routing\\Exceptions\\UrlGenerationException::forMissingParameters(Object(Illuminate\\Routing\\Route), Array)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(500): Illuminate\\Routing\\RouteUrlGenerator->to(Object(Illuminate\\Routing\\Route), Array, true)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(469): Illuminate\\Routing\\UrlGenerator->toRoute(Object(Illuminate\\Routing\\Route), Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('user.template.e...', Array, true)
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\storage\\framework\\views\\c5dc76255bd159b241ad31954df18724.php(35): route('user.template.e...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}
"} 
[2025-05-31 10:18:40] local.ERROR: Route [user.template.delete] not defined. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\resources\\views\\user\\template\\view.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1663720550 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1958</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1663720550\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","device":"<pre class=sf-dump id=sf-dump-241711186 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Device</span> {<a class=sf-dump-ref>#1999</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">devices</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>uuid</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5a568965-12af-4eb2-b2eb-************</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NXC Controls</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"
    \"<span class=sf-dump-key>user_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>qr</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>meta</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>waid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>phoneid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>msg_limit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>quality_rating</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0b6345e5-710a-438f-af94-fd810d025250</span>\"
    \"<span class=sf-dump-key>t_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>t_bot_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>auto_send_mkt_fail_msg</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>typebot</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>uuid</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5a568965-12af-4eb2-b2eb-************</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NXC Controls</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"
    \"<span class=sf-dump-key>user_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>qr</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>meta</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>waid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>phoneid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>msg_limit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>quality_rating</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0b6345e5-710a-438f-af94-fd810d025250</span>\"
    \"<span class=sf-dump-key>t_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>t_bot_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>auto_send_mkt_fail_msg</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>typebot</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">uuid</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-241711186\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","templates":"<pre class=sf-dump id=sf-dump-1702916534 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2008</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2010</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2011</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2012</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2013</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>4</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2014</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>5</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2015</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>6</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2016</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1702916534\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","total_templates":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>7</span>
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.template.delete] not defined. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('user.template.d...', Array, true)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\resources\\views\\user\\template\\view.blade.php(37): route('user.template.d...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.template.delete] not defined. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('user.template.d...', Array, true)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\storage\\framework\\views\\c5dc76255bd159b241ad31954df18724.php(37): route('user.template.d...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}
"} 
[2025-05-31 10:18:46] local.ERROR: Route [user.template.delete] not defined. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\resources\\views\\user\\template\\view.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1395127481 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1958</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1395127481\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","device":"<pre class=sf-dump id=sf-dump-1755224980 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Device</span> {<a class=sf-dump-ref>#1999</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">devices</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>uuid</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5a568965-12af-4eb2-b2eb-************</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NXC Controls</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"
    \"<span class=sf-dump-key>user_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>qr</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>meta</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>waid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>phoneid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>msg_limit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>quality_rating</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0b6345e5-710a-438f-af94-fd810d025250</span>\"
    \"<span class=sf-dump-key>t_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>t_bot_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>auto_send_mkt_fail_msg</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>typebot</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>uuid</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5a568965-12af-4eb2-b2eb-************</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NXC Controls</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">************</span>\"
    \"<span class=sf-dump-key>user_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>qr</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>meta</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>waid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>phoneid</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>msg_limit</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>quality_rating</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0b6345e5-710a-438f-af94-fd810d025250</span>\"
    \"<span class=sf-dump-key>t_user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>t_bot_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>auto_send_mkt_fail_msg</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>typebot</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 18:39:00</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">uuid</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-1755224980\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","templates":"<pre class=sf-dump id=sf-dump-164761039 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2008</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2010</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2011</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2012</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2013</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>4</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2014</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>5</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2015</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>6</span> => <span class=sf-dump-note title=\"App\\Models\\Template
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Template</span> {<a class=sf-dump-ref>#2016</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-164761039\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","total_templates":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>7</span>
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [user.template.delete] not defined. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('user.template.d...', Array, true)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\resources\\views\\user\\template\\view.blade.php(37): route('user.template.d...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [user.template.delete] not defined. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('user.template.d...', Array, true)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\storage\\framework\\views\\c5dc76255bd159b241ad31954df18724.php(37): route('user.template.d...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(71): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}
"} 
