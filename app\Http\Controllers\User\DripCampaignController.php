<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Jobs\BulkInsertTaskJob;
use App\Models\Device;
use App\Models\DripCampaign;
use App\Models\Greetings;
use App\Models\Template;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class DripCampaignController extends Controller
{
    public function index(Request $request)
    {
        // as per device check with auth user
        $drips = DripCampaign::whereHas('device', function ($query) {
            $query->where('user_id', Auth::id());
        })->latest()->get();

        $type = $request->type ?? '';

        $device_data = Device::where('user_id', Auth::id())->where('status', 1)->get();

        return view('user.drip_campaign.index', compact('device_data', 'drips', 'type'));
    }

    public function create()
    {
        // $devices = Device::where('user_id', Auth::id())->where('status', 1)->latest()->get();
        $devices = Device::where('user_id', Auth::id())->where('status', 1)->get();

        $templates = Template::where('user_id', Auth::id())->where('status', 1)->get();

        return view('user.drip_campaign.create', compact('devices', 'templates'));
    }

    public function store(Request $request)
    {
        // dd($request->all());
        $validated = $request->validate([
            'drip_name' => 'required',
            'device_id' => 'required',
            'drip_count' => 'required|array',
            'drip_count.*' => 'required|string',
            // 'days' => 'required|array',
            'days' => 'required|array|min:1',
            'days.*' => 'required|integer|min:1|max:60',
            'drip_template' => 'required|array',
            'drip_template.*' => 'required|string',
            // 'drip_template' => 'required|array',
            // 'b_language' => 'required|array',
            // 'b_language' => 'required|array',
            // 'b_language.*' => 'required|string',
            // 'b_task_type' => 'required|array',
            // 'b_task_type.*' => 'required|string',
            // 'has_media' => 'required|array',
            // 'has_media.*' => 'required|in:0,1',
        ], [
            'drip_name.required' => 'The drip name is required.',
            'device_id.required' => 'The device is required.',
            'days.*.required' => 'The days field is required.',
            'days.*.integer' => 'The days field must be an integer.',
            'days.*.min' => 'The days field must be at least :min.',
            'days.*.max' => 'The days field may not be greater than :max.',
            'drip_template.*.required' => 'The template field is required.',
            'b_language.*.required' => 'The language field is required.',
            'b_task_type.*.required' => 'The task type field is required.',
            'has_media.*.required' => 'The media field is required.',
            'has_media.*.in' => 'The media field must be either 0 or 1.',
        ]);

        $drip_data = [];
        $media_files = [];

        if ($request->hasFile('media_file')) {
            foreach ($request->file('media_file') as $index => $file) {
                if ($file && $request->has_media[$index] == 1) {
                    $media_path = $this->uploadMediaFile($file);
                    if (!$media_path) {
                        return response()->json([
                            'message' => __('Failed to upload media file at position ' . ($index + 1)),
                        ], 401);
                    }
                    $media_files[$index] = asset($media_path);
                }
            }
        }

        // Combine all drip data into structured format
        for ($i = 0; $i < count($request->drip_count); $i++) {
            $drip_entry = [
                'drip_count' => $request->drip_count[$i],
                'days' => $request->days[$i],
                'template' => $request->drip_template[$i],
                'language' => $request->b_language[$i],
                'task_type' => $request->b_task_type[$i],
                'has_media' => $request->has_media[$i],
                'media_path' => $media_files[$i] ?? null,
            ];

            $drip_data[] = $drip_entry;
        }

        try {
            // Create new drip campaign
            $drip = new DripCampaign();
            $drip->drip_name = $request->drip_name;
            $drip->device_id = $request->device_id;
            $drip->drip_data = json_encode($drip_data);
            $drip->save();

            return response()->json([
                'redirect' => route('user.drip_campaign.index'),
                'message' => __('Drip Campaign Created Successfully')
            ], 200);
        } catch (\Exception $e) {
            // Clean up uploaded files if save fails
            foreach ($media_files as $file) {
                if (file_exists($file)) {
                    unlink($file);
                }
            }

            return response()->json([
                'message' => __('Failed to create drip campaign: ') . $e->getMessage()
            ], 422);
        }
    }

    private function uploadMediaFile($file)
    {
        try {
            $fileName = uniqid() . '.' . $file->getClientOriginalExtension();
            // $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = 'uploads/drip_campaign/';

            // Create directory if it doesn't exist
            if (!File::isDirectory(public_path($filePath))) {
                File::makeDirectory(public_path($filePath), 0777, true);
            }

            // Move file to storage
            $file->move(public_path($filePath), $fileName);

            return $filePath . $fileName;
        } catch (\Exception $e) {
            Log::error('Media upload failed: ' . $e->getMessage());
            return false;
        }
    }

    public function getDripName(Request $request)
    {
        $validated = $request->validate([
            'device_id' => 'required',
        ], [
            'device_id.required' => 'The device is required.',
        ]);

        // dd($request->all());
        $drip_data = DripCampaign::select('id', 'drip_name')
            ->where('device_id', $request->device_id)
            ->get()
            ->toArray();
        // dd($drip_name);

        return response()->json([
            'drip_data' => $drip_data,
        ]);
    }

    public function sendDrip(Request $request)
    {
        // dd($request->all());

        $validated = $request->validate([
            'drip_device' => 'required',
            'drip_name' => 'required'
        ], [
            'drip_device.required' => 'The device is required.',
            'drip_name.required' => 'The drip name is required.',
        ]);

        $deviceId = $request->drip_device;
        $dripId = $request->drip_name;
        $drip_campaign = DripCampaign::where('device_id', $deviceId)
            ->where('id', $dripId)
            ->first();
        // dd($drip_campaign);

        if (!$drip_campaign) {
            return response()->json([
                'message' => __('Drip campaign not found for the selected device.'),
            ], 404);
        }

        // json_decode the drip data
        $drip_data = json_decode($drip_campaign->drip_data, true);
        // dd('jsondecode drip data',$drip_data);

        // Get the device details
        // $device = Device::find($deviceId);
        // dd('device',$deviceId);

        //campaign numbers
        $campaign_numbers = $request->input('campaign_numbers');
        // dd('campaign_numbers', $campaign_numbers);
        if (is_array($campaign_numbers)) {
            $campaign_numbers = implode(PHP_EOL, $campaign_numbers);
        }

        $campaign_numbers_array = explode(PHP_EOL, $campaign_numbers);

        $campaign_numbers_array = array_filter($campaign_numbers_array, 'trim');
        //remove all special characters and alphabets
        $campaign_numbers_array = array_map(function ($item) {
            return preg_replace('/[^0-9]/', '', $item);
        }, $campaign_numbers_array);

        $campaign_numbers_array = array_unique($campaign_numbers_array);

        $campaign_numbers_array = array_filter($campaign_numbers_array, function ($item) {
            return strlen($item) >= 10;
        });

        // json decode drip data using foreach loop
        foreach ($drip_data as $drip) {
            // dd($drip['days']);
            //$drip['days'] = 1; then schedule today
            if ($drip['days'] === '1') {
                $scheduled_on = now();
            } else {
                //schedule time for each drip get from the days
                $scheduled_on = now()->addDays($drip['days']);
            }
            //schedule time for each drip get from the days
            //$scheduled_on = now()->addDays($drip['days']);
            $ip = request()->ip();
            $taskdata = [
                'device_id' => (int)$deviceId,
                'created_by' => Auth::id(),
                'launched_on' => now(),
                'scheduled_on' => $scheduled_on,
                'task_url' => $drip['media_path'],
                'campaign_name' => $drip_campaign->drip_name,
                'templateId' => $drip['template'],
                'language' => $drip['language'],
                'task_type' => $drip['task_type'],
                'parameters' => null,
                'flow_id' => null,
                'text' => null,
                'ip' => $ip,
            ];

            try {
                BulkInsertTaskJob::dispatch($taskdata, $campaign_numbers_array)->onQueue('high');
            } catch (\Exception $e) {
                return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
            }
        };
        // dd('taskdata', $taskdata);
        // dd('taskdata', $taskdata);
        // dd('campaign_numbers_array', $campaign_numbers_array);
        // dd('drip', $drip);
        // dd('drip_campaign', $drip_campaign);

        return response()->json([
            'redirect' => route('user.drip_campaign.index'),
            'message' => __('Drip Campaign Sent Successfully')
        ], 200);
    }

    public function edit($id)
    {
        $drip_campaign = DripCampaign::find($id);
        // dd($drip_campaign);
        if ($drip_campaign) {
            // $devices = Device::where('user_id', Auth::id())->where('status', 1)->latest()->get();
            $devices = Device::where('user_id', Auth::id())->where('status', 1)->get();

            $templates = Template::where('user_id', Auth::id())->where('status', 1)->get();

            // $device_data = [];
            // $i = 0;
            // foreach ($devices  as $device) {

            //     $templates = getFacebookTemplates($device->waid, $device->token);
            //     // $approved_templates = array_filter($templates, function ($template) {
            //     //     return $template['status'] === 'APPROVED';
            //     // });
            //     // dd($templates);
            //     $filtered_templates = [];

            //     foreach ($templates as $template) {
            //         // Only process templates with "APPROVED" status
            //         if ($template['status'] == "APPROVED") {
            //             // Initialize a flag to track if the template is simple (no dynamic content)
            //             $isSimpleTemplate = true;

            //             foreach ($template['components'] as $component) {
            //                 if ($component['type'] == "BODY") {
            //                     // Check if the BODY text contains dynamic placeholders {{}} (i.e., not simple)
            //                     if (strpos($component['text'], "{{") !== false) {
            //                         $isSimpleTemplate = false; // Mark this template as not simple
            //                         break; // No need to check further components for this template
            //                     }
            //                 }

            //                 if ($component['type'] == "BUTTONS") {
            //                     foreach ($component['buttons'] as $button) {
            //                         if (strcasecmp($button['type'], "url") === 0) {
            //                             // Check if the URL contains dynamic placeholders {{}} (i.e., not simple)
            //                             if (strpos($button['url'], "{{") !== false) {
            //                                 $isSimpleTemplate = false; // Mark this template as not simple
            //                                 break 2; // Break out of both loops
            //                             }
            //                         }
            //                     }
            //                 }
            //             }

            //             // If the template is simple (does not contain any dynamic placeholders), add it to filtered list
            //             if ($isSimpleTemplate) {
            //                 $filtered_templates[] = $template;
            //             }
            //         }
            //     }

            //     $id = $device->id;
            //     $device_data[$id]['uuid'] = $device->uuid;
            //     $device_data[$id]['templates'] = $filtered_templates;
            //     $device_data[$id]['id'] = $device->id;
            //     $device_data[$id]['name'] = $device->name;
            //     $device_data[$id]['phone'] = $device->phone;
            //     $i++;
            // }

            // $dripData = Greetings::where('user_id', Auth::id())
            //     ->get();
            $drip_campaign->drip_data = json_decode($drip_campaign->drip_data, true);
            // dd($drip_campaign->drip_data);

            return view('user.drip_campaign.edit', compact('devices', 'templates', 'drip_campaign'));
        } else {
            return redirect()->back()->with('error', 'Drip Campaign not found');
        }
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'drip_name' => 'required',
            'device_id' => 'required',
            'drip_count' => 'required|array',
            'drip_count.*' => 'required|string',
            'days' => 'required|array|min:1',
            'days.*' => 'required|integer|min:1|max:60',
            'drip_template' => 'required|array',
            'drip_template.*' => 'required|string',
            'b_language' => 'required|array',
            'b_language.*' => 'required|string',
            'b_task_type' => 'required|array',
            'b_task_type.*' => 'required|string',
            'has_media' => 'required|array',
            'has_media.*' => 'required|in:0,1',
        ], [
            'drip_name.required' => 'The drip name is required.',
            'device_id.required' => 'The device is required.',
            'days.*.required' => 'The days field is required.',
            'days.*.integer' => 'The days field must be an integer.',
            'days.*.min' => 'The days field must be at least :min.',
            'days.*.max' => 'The days field may not be greater than :max.',
            'drip_template.*.required' => 'The template field is required.',
            'b_language.*.required' => 'The language field is required.',
            'b_task_type.*.required' => 'The task type field is required.',
            'has_media.*.required' => 'The media field is required.',
            'has_media.*.in' => 'The media field must be either 0 or 1.',
        ]);

        $drip = DripCampaign::find($id);
        if (!$drip) {
            return response()->json([
                'message' => __('Drip Campaign not found'),
            ], 404);
        }

        // Get existing drip data
        $existing_drip_data = json_decode($drip->drip_data, true);
        $drip_data = [];
        $media_files = [];
        $new_uploads = [];

        // Process media files
        if ($request->hasFile('media_file')) {
            foreach ($request->file('media_file') as $index => $file) {
                if ($file && $request->has_media[$index] == 1) {
                    $media_path = $this->uploadMediaFile($file);
                    if (!$media_path) {
                        return response()->json([
                            'message' => __('Failed to upload media file at position ' . ($index + 1)),
                        ], 401);
                    }
                    $media_files[$index] = asset($media_path);
                    $new_uploads[] = $media_path; // Track new uploads for cleanup if needed
                }
            }
        }

        // Combine all drip data into structured format
        for ($i = 0; $i < count($request->drip_count); $i++) {
            // Determine the media path:
            // 1. Use newly uploaded file if exists
            // 2. Use existing media path if available and has_media is 1
            // 3. Set to null if has_media is 0
            $media_path = null;
            if (isset($media_files[$i])) {
                // New file uploaded
                $media_path = $media_files[$i];
            } elseif ($request->has_media[$i] == 1 && isset($existing_drip_data[$i]['media_path'])) {
                // No new file, keep existing media if has_media is 1
                $media_path = $existing_drip_data[$i]['media_path'];
            }

            $drip_entry = [
                'drip_count' => $request->drip_count[$i],
                'days' => $request->days[$i],
                'template' => $request->drip_template[$i],
                'language' => $request->b_language[$i],
                'task_type' => $request->b_task_type[$i],
                'has_media' => $request->has_media[$i],
                'media_path' => $media_path
            ];

            $drip_data[] = $drip_entry;
        }

        try {
            // Update drip campaign
            $drip->drip_name = $request->drip_name;
            $drip->device_id = $request->device_id;
            $drip->drip_data = json_encode($drip_data);
            $drip->save();

            return response()->json([
                'redirect' => route('user.drip_campaign.index'),
                'message' => __('Drip Campaign Updated Successfully')
            ], 200);
        } catch (\Exception $e) {
            // Clean up only newly uploaded files if save fails
            foreach ($new_uploads as $file) {
                $file_path = public_path(str_replace(asset(''), '', $file));
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
            }

            return response()->json([
                'message' => __('Failed to update drip campaign: ') . $e->getMessage()
            ], 422);
        }
    }

    public function destroy($id)
    {
        // Find the DripCampaign by ID
        $drip = DripCampaign::find($id);

        if ($drip) {
            // Check if drip_data exists and contains media paths
            if ($drip->drip_data) {
                $dripData = json_decode($drip->drip_data, true); // Decode the JSON string

                // Loop through each drip data entry and delete media files
                foreach ($dripData as $data) {
                    // dd($data['media_path']);
                    if (!empty($data['media_path'])) {
                        // Extract the relative file path from the media_path
                        // Assuming the URL has the base path 'http://*************/waba_panelv2/public/uploads/drip_campaign/'
                        $baseUrl = url('/'); // Get the base URL dynamically (e.g., 'http://example.com')
                        $relativePath = str_replace($baseUrl, '', $data['media_path']); // Remove the base URL from the full media path

                        // Get the full file path on the server using public_path
                        $filePath = public_path($relativePath);

                        // Check if the file exists and delete it
                        if (file_exists($filePath)) {
                            unlink($filePath); // Delete the file
                        }
                    }
                }
            }

            // Delete the DripCampaign
            $drip->delete();

            return response()->json([
                'redirect' => route('user.drip_campaign.index'),
                'message' => __('Drip Campaign Deleted Successfully')
            ], 200);
        } else {
            // If DripCampaign not found
            return redirect()->back()->with('error', 'Drip Campaign not found');
        }
    }
}
