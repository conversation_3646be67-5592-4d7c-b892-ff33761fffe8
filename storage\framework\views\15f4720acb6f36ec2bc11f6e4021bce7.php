
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Send Whatsapp'),
        'buttons' => isset($videoTutorial->value)
            ? [
                [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp;&nbsp;' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ],
            ]
            : [],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
    <style>
        /* RCS Template Preview Styles */
        .rcs-message-bubble {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 3px solid #007bff;
        }

        .rcs-rich-card {
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .rcs-card-media img {
            border-radius: 8px;
        }

        .rcs-carousel {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
        }

        .rcs-carousel-card {
            background-color: #ffffff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .rcs-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .rcs-suggestions .btn {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        .rcs-media-container {
            text-align: center;
        }

        .rcs-media-container img {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Additional styles for enhanced RCS preview */
        .rcs-card-suggestions {
            margin-top: 8px;
        }

        .rcs-card-suggestions .btn {
            font-size: 0.7rem;
            padding: 2px 6px;
            margin: 1px;
        }

        /* Horizontal card layout */
        .rcs-rich-card.d-flex {
            align-items: flex-start;
        }

        /* Carousel scrollbar styling */
        .rcs-carousel .d-flex::-webkit-scrollbar {
            height: 6px;
        }

        .rcs-carousel .d-flex::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .rcs-carousel .d-flex::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        .rcs-carousel .d-flex::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Action button icons */
        .rcs-suggestions .btn {
            transition: all 0.2s ease;
        }

        .rcs-suggestions .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">

        <div class="col-md-8">
            <?php if(api_plan() && api_plan()->title == 'Api'): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>

                                <?php echo e(__('Send whatsapp features is not available in your subscription plan')); ?>


                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <?php if(Session::has('error')): ?>
                <div class="alert bg-gradient-danger text-white alert-dismissible fade show success-alert"
                    id="success-alert" role="alert">
                    <span class="alert-text"><?php echo e(Session::get('error')); ?></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
            <?php elseif(Session::has('success')): ?>
                <div class="alert bg-gradient-success text-white alert-dismissible fade show success-alert" id="error-alert"
                    role="alert">
                    <span class="alert-text"><?php echo e(Session::get('success')); ?></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Modal for YouTube video -->
            <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                                allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <form id="add_camp_history_second" class="add_form" action="<?php echo e(route('user.sent.customtext')); ?>"
                        method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Select Device')); ?></label>
                            <div class="col-sm-9">
                                <select class="form-control campaign_device" id="campaign_device" name="campaign_device"
                                    required="" data-toggle="select">
                                    <option value=""><?php echo e(__('-- Select Device --')); ?></option>
                                    <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?>

                                            <?php if(!empty($device['phone'])): ?>
                                                (+<?php echo e($device['phone']); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Select Template')); ?></label>
                            <div class="col-sm-9">
                                <select class="form-control template_select" id="template_select" name="template_select"
                                    required="" data-toggle="select">
                                    
                                    
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Campaign Name')); ?></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" name="campaign_name" id="campaign_name"
                                    placeholder="Campaign Name">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Schedule on')); ?></label>
                            <div class="col-sm-9">
                                <input type="datetime-local" name="schedule_on" id="schedule_on" class="form-control"
                                    placeholder="Please choose date & time..." min="<?php echo e(now()->format('d-m-Y')); ?>">
                            </div>
                        </div>
                        <div class="form-group row" id="msg_media" style="display: none;">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Media')); ?> <small
                                    class="text-danger"><?php echo e(__('(Max Size 5 MB, Max Width 1024, MAX Height 768)')); ?></small></label>
                            <div class="col-sm-9">
                                <input type="file" class="form-control" name="media_file" id="media_file" disabled>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Mobile Numbers')); ?></label>
                            <div class="col-sm-9">
                                <textarea name="campaign_numbers[]" class="form-control" type="number"
                                    placeholder="<?php echo e(__('Enter phone number with country code')); ?>" id="campaign_numbers" oninput="cleanInput()"
                                    cols="70" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Campaign Groups')); ?></label>
                            <div class="col-sm-9">
                                <select class="form-control campaign_groups" id="campaign_groups"
                                    name="campaign_groups[]" multiple="">
                                    <option disabled><?php echo e(__('-- Select Campaign Groups --')); ?></option>
                                    <?php $__currentLoopData = $group_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($group->id); ?>"><?php echo e($group->name); ?> -
                                            <?php echo e($group->total_member); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="text-center">
                            <button class="btn btn-outline-primary submit-button float-right" type="submit"
                                id="submit_btn"><?php echo e(__('Send Message')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="card"
            style="width: 18rem; background-image: url('<?php echo e(asset('assets/img/whatsappback.jpg')); ?>'); background-size: cover;">
            
            <div class="card-body" id="template-content">
                
                
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script src="https://woody180.github.io/vanilla-javascript-emoji-picker/vanillaEmojiPicker.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit-icons.min.js"></script>

    <script src="<?php echo e(asset('assets/js/pages/user/schedule-create.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('assets/js/pages/bulk/template.js')); ?>"></script>

    <script>
        $(".campaign_groups").select2({
            placeholder: "<?php echo e(__('Select Campaign Groups')); ?>",
            allowClear: true
        });
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
    
    <script>
        setTimeout(function() {
            document.getElementById('success-alert')?.remove();
            document.getElementById('error-alert')?.remove();
        }, 10000);
    </script>
    <script>
        $(document).ready(function() {
            function selectSingleDevice() {
                var deviceDropdown = $("#campaign_device");
                if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                    deviceDropdown.children("option").eq(1).prop('selected', true);
                    deviceDropdown.change();
                }
            }
            selectSingleDevice();
        });

        $('#campaign_device').on('change', function() {
            var selectedDevice = $(this).val();
            $('#template_select').empty().append(
                '<option value="" >-- Select Template --</option>');

            if (!selectedDevice) return;

            $.ajax({
                url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id',
                    selectedDevice),
                type: 'GET',
                success: function(templates) {
                    $.each(templates, function(index, value) {
                        if (value.status == "1") {
                            $('#template_select').append("<option value='" + value
                                .id +
                                "'>" + value.title + " -- " + value.type +
                                "</option>");
                        }
                    });
                },
                error: function(xhr) {
                    console.error('AJAX error:', xhr.responseText);
                }
            });
        });


        $('#template_select').change(function() {
            var selectedTemplateId = $(this).val();

            if (!selectedTemplateId) {
                $('#template-content').empty();
                return;
            }

            // Fetch template details via AJAX for better performance
            var selectedDevice = $('#campaign_device').val();
            if (!selectedDevice) return;

            $.ajax({
                url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id',
                    selectedDevice),
                type: 'GET',
                success: function(templates) {
                    var selectedTemplate = templates.find(function(template) {
                        return template.id == selectedTemplateId;
                    });

                    if (selectedTemplate) {
                        displaySelectedTemplate(selectedTemplate);
                    }
                },
                error: function(xhr) {
                    console.error('AJAX error:', xhr.responseText);
                    $('#template-content').html(
                        '<p class="text-danger">Error loading template preview</p>');
                }
            });
        });

        function displaySelectedTemplate(template) {
            // Clear previous template content
            $('#template-content').empty();

            if (!template) {
                return;
            }

            // Add template title
            if (template.title) {
                $('#template-content').append('<h4 class="card-title mb-3">' + template.title + '</h4>');
            }

            // Handle different template types
            if (template.type === 'plain-text') {
                displayPlainTextTemplate(template);
            } else if (template.type === 'text-with-media') {
                displayMediaTemplate(template);
            } else if (template.type === 'rich-card') {
                displayRichCardTemplate(template);
            } else if (template.type === 'rich-card-carousel') {
                displayCarouselTemplate(template);
            } else {
                // Legacy template types (numeric)
                displayLegacyTemplate(template);
            }
        }

        function displayPlainTextTemplate(template) {
            if (template.body && template.body.content && template.body.content.plainText) {
                $('#template-content').append(
                    '<div class="rcs-message-bubble">' +
                    '<p class="card-text">' + formatWhatsAppText(template.body.content.plainText) + '</p>' +
                    '</div>'
                );
            }

            // Display suggestions if any
            if (template.body && template.body.content && template.body.content.suggestions) {
                displaySuggestions(template.body.content.suggestions);
            }
        }

        function displayMediaTemplate(template) {
            if (template.body && template.body.content) {
                var content = template.body.content;

                // Display media if present
                if (content.rcs_media_url || content.local_media_url) {
                    var mediaUrl = content.rcs_media_url || content.local_media_url;
                    $('#template-content').append(
                        '<div class="rcs-media-container mb-2">' +
                        '<img src="' + mediaUrl +
                        '" class="img-fluid rounded" style="max-width: 240px; max-height: 200px;">' +
                        '</div>'
                    );
                }

                // Display text
                if (content.plainText) {
                    $('#template-content').append(
                        '<div class="rcs-message-bubble">' +
                        '<p class="card-text">' + formatWhatsAppText(content.plainText) + '</p>' +
                        '</div>'
                    );
                }

                // Display suggestions
                if (content.suggestions) {
                    displaySuggestions(content.suggestions);
                }
            }
        }

        function displayRichCardTemplate(template) {
            if (template.body && template.body.content && template.body.content.richCardDetails) {
                var richCard = template.body.content.richCardDetails.standalone;
                if (richCard && richCard.content) {
                    var cardContent = richCard.content;

                    // Create rich card container with orientation support
                    var orientation = richCard.cardOrientation || 'VERTICAL';
                    var cardClass = orientation === 'HORIZONTAL' ? 'd-flex' : '';
                    var cardHtml = '<div class="rcs-rich-card border rounded p-3 mb-2 ' + cardClass +
                        '" style="max-width: 280px;">';

                    // Display card media if present
                    if (cardContent.cardMedia && cardContent.cardMedia.contentInfo && cardContent.cardMedia.contentInfo
                        .fileUrl) {
                        var mediaHeight = cardContent.cardMedia.mediaHeight || 'MEDIUM';
                        var mediaStyle = orientation === 'HORIZONTAL' ?
                            'width: 120px; height: 80px; object-fit: cover; margin-right: 10px;' :
                            'width: 100%; height: 150px; object-fit: cover;';

                        cardHtml += '<div class="rcs-card-media mb-2">';
                        cardHtml += '<img src="' + cardContent.cardMedia.contentInfo.fileUrl +
                            '" class="img-fluid rounded" style="' + mediaStyle + '">';
                        cardHtml += '</div>';
                    }

                    // Content container for horizontal layout
                    if (orientation === 'HORIZONTAL') {
                        cardHtml += '<div class="flex-grow-1">';
                    }

                    // Display card title
                    if (cardContent.cardTitle) {
                        cardHtml += '<h6 class="rcs-card-title font-weight-bold mb-1">' + cardContent.cardTitle + '</h6>';
                    }

                    // Display card description
                    if (cardContent.cardDescription) {
                        cardHtml += '<p class="rcs-card-description text-muted small mb-2">' + formatWhatsAppText(
                            cardContent.cardDescription) + '</p>';
                    }

                    // Close content container for horizontal layout
                    if (orientation === 'HORIZONTAL') {
                        cardHtml += '</div>';
                    }

                    cardHtml += '</div>';
                    $('#template-content').append(cardHtml);

                    // Display suggestions if any (they are optional)
                    if (cardContent.suggestions && cardContent.suggestions.length > 0) {
                        displaySuggestions(cardContent.suggestions);
                    }
                }
            }
        }

        function displayCarouselTemplate(template) {
            if (template.body && template.body.content && template.body.content.richCardDetails && template.body.content
                .richCardDetails.carousel) {
                var carousel = template.body.content.richCardDetails.carousel;

                if (carousel.contents && carousel.contents.length > 0) {
                    var cardWidth = carousel.cardWidth || 'MEDIUM_WIDTH';
                    var cardWidthPx = cardWidth === 'SMALL_WIDTH' ? '150px' : (cardWidth === 'MEDIUM_WIDTH' ? '180px' :
                        '220px');

                    var carouselHtml = '<div class="rcs-carousel mb-2">';
                    carouselHtml += '<div class="d-flex" style="overflow-x: auto; gap: 10px; padding-bottom: 10px;">';

                    carousel.contents.forEach(function(card, index) {
                        carouselHtml +=
                            '<div class="rcs-carousel-card border rounded p-2 flex-shrink-0" style="min-width: ' +
                            cardWidthPx + '; max-width: ' + cardWidthPx + ';">';

                        // Display card media
                        if (card.cardMedia && card.cardMedia.contentInfo && card.cardMedia.contentInfo.fileUrl) {
                            var mediaHeight = card.cardMedia.mediaHeight || 'MEDIUM';
                            var heightPx = mediaHeight === 'SHORT' ? '80px' : (mediaHeight === 'MEDIUM' ? '100px' :
                                '120px');

                            carouselHtml += '<div class="rcs-card-media mb-2">';
                            carouselHtml += '<img src="' + card.cardMedia.contentInfo.fileUrl +
                                '" class="img-fluid rounded" style="width: 100%; height: ' + heightPx +
                                '; object-fit: cover;">';
                            carouselHtml += '</div>';
                        }

                        // Display card title
                        if (card.cardTitle) {
                            carouselHtml += '<h6 class="rcs-card-title font-weight-bold small mb-1">' + card
                                .cardTitle + '</h6>';
                        }

                        // Display card description
                        if (card.cardDescription) {
                            carouselHtml +=
                                '<p class="rcs-card-description text-muted mb-2" style="font-size: 0.75rem;">' +
                                formatWhatsAppText(card.cardDescription) + '</p>';
                        }

                        // Display individual card suggestions if any (they are optional)
                        if (card.suggestions && card.suggestions.length > 0) {
                            carouselHtml += '<div class="rcs-card-suggestions">';
                            card.suggestions.forEach(function(suggestion) {
                                if (suggestion.reply && suggestion.reply.plainText) {
                                    carouselHtml +=
                                        '<button class="btn btn-outline-primary btn-sm mr-1 mb-1" style="font-size: 0.7rem; padding: 2px 6px;">' +
                                        suggestion.reply.plainText + '</button>';
                                } else if (suggestion.action && suggestion.action.plainText) {
                                    carouselHtml +=
                                        '<button class="btn btn-outline-secondary btn-sm mr-1 mb-1" style="font-size: 0.7rem; padding: 2px 6px;">' +
                                        suggestion.action.plainText + '</button>';
                                }
                            });
                            carouselHtml += '</div>';
                        }

                        carouselHtml += '</div>';
                    });

                    carouselHtml += '</div></div>';
                    $('#template-content').append(carouselHtml);

                    // Show carousel meta info if available
                    if (template.body._carousel_meta && template.body._carousel_meta.total_cards) {
                        $('#template-content').append('<small class="text-muted">Carousel with ' + template.body
                            ._carousel_meta.total_cards + ' cards</small>');
                    }
                }
            }
        }

        function displayLegacyTemplate(template) {
            // Handle legacy numeric template types
            if (template.type == 2) {
                // Image template
                if (template.media_url) {
                    $('#template-content').append(
                        '<img class="card-img-top img-fluid rounded mb-2" src="' + template.media_url +
                        '" alt="' + template.title + '" style="max-width: 240px; max-height: 200px;">'
                    );
                }
            } else if (template.type == 3) {
                // Document template
                if (template.media_url) {
                    $('#template-content').append(
                        '<a href="' + template.media_url +
                        '" target="_blank" class="btn btn-outline-primary btn-sm mb-2">' +
                        '<i class="fa fa-file"></i> View Document' +
                        '</a>'
                    );
                }
            } else if (template.type == 4) {
                // Video template
                if (template.media_url) {
                    $('#template-content').append(
                        '<video width="240" height="180" controls class="rounded mb-2">' +
                        '<source src="' + template.media_url + '" type="video/mp4">' +
                        'Your browser does not support the video tag.' +
                        '</video>'
                    );
                }
            }

            // Display body text for legacy templates
            if (template.body) {
                $('#template-content').append(
                    '<p class="card-text">' + formatWhatsAppText(template.body) + '</p>'
                );
            }

            // Display buttons for legacy templates
            if (Array.isArray(template.buttons) && template.buttons.length > 0) {
                var buttonHtml = '<div class="rcs-suggestions mt-2">';
                template.buttons.forEach(function(button) {
                    buttonHtml += '<button class="btn btn-outline-primary btn-sm mr-1 mb-1">' + button
                        .text + '</button>';
                });
                buttonHtml += '</div>';
                $('#template-content').append(buttonHtml);
            }
        }

        function displaySuggestions(suggestions) {
            if (suggestions && suggestions.length > 0) {
                var suggestionsHtml = '<div class="rcs-suggestions mt-2">';

                suggestions.forEach(function(suggestion) {
                    // Handle reply suggestions (from your JSON structure)
                    if (suggestion.reply && suggestion.reply.plainText) {
                        suggestionsHtml += '<button class="btn btn-outline-primary btn-sm mr-1 mb-1">' + suggestion
                            .reply.plainText + '</button>';
                    }
                    // Handle action suggestions (like location, dialer, etc.)
                    else if (suggestion.action && suggestion.action.plainText) {
                        var buttonClass = 'btn-outline-secondary';
                        var buttonText = suggestion.action.plainText;

                        // Different styling for different action types
                        if (suggestion.action.showLocation) {
                            buttonClass = 'btn-outline-info';
                            buttonText += ' 📍';
                        } else if (suggestion.action.dialPhoneNumber) {
                            buttonClass = 'btn-outline-success';
                            buttonText += ' 📞';
                        } else if (suggestion.action.openUrl) {
                            buttonClass = 'btn-outline-warning';
                            buttonText += ' 🔗';
                        }

                        suggestionsHtml += '<button class="btn ' + buttonClass + ' btn-sm mr-1 mb-1">' +
                            buttonText + '</button>';
                    }
                    // Handle legacy format (fallback)
                    else if (suggestion.reply && suggestion.reply.text) {
                        suggestionsHtml += '<button class="btn btn-outline-primary btn-sm mr-1 mb-1">' + suggestion
                            .reply.text + '</button>';
                    } else if (suggestion.action && suggestion.action.text) {
                        suggestionsHtml += '<button class="btn btn-outline-secondary btn-sm mr-1 mb-1">' +
                            suggestion.action.text + '</button>';
                    }
                });

                suggestionsHtml += '</div>';
                $('#template-content').append(suggestionsHtml);
            }
        }

        function formatWhatsAppText(text) {
            if (!text) return '';

            // Replace newlines with <br>
            text = text.replace(/\n/g, '<br>');

            // Convert asterisks to <b> tags for bold text
            text = text.replace(/\*(.*?)\*/g, '<b>$1</b>');

            // Convert underscores to <i> tags for italic text
            text = text.replace(/_(.*?)_/g, '<i>$1</i>');

            // Convert backticks to <code> tags for monospace text
            text = text.replace(/```(.*?)```/g, '<code>$1</code>');

            // Convert tildes to <strike> tags for strike-through text
            text = text.replace(/~~(.*?)~~/g, '<strike>$1</strike>');

            return text;
        }
    </script>
    <script>
        function cleanInput() {
            const textarea = document.getElementById('campaign_numbers');
            // Remove whitespace, special characters, and alphabets
            textarea.value = textarea.value.replace(/[^\d\n]/g, '');
        }
    </script>

    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/singlesend/create.blade.php ENDPATH**/ ?>